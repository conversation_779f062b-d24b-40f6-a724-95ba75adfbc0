<?php
/**
 * Fișier de compatibilitate pentru DosarService
 * Încarcă bootstrap-ul și apoi noua clasă DosarService
 */

// Încărcăm bootstrap-ul pentru a inițializa configurația
require_once dirname(__DIR__) . '/bootstrap.php';

// Acum încărcăm noua clasă DosarService care va avea acces la configurație
// Clasa este deja încărcată prin autoloader din bootstrap.php

// Pentru compatibilitate cu codul vechi, creăm un alias global
if (!class_exists('DosarService', false)) {
    class_alias('App\Services\DosarService', 'DosarService');
}
// Momentan, doar includem fișierul nou
?>
