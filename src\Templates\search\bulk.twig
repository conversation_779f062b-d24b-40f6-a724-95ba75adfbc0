{% extends "layouts/main.twig" %}

{% block title %}{{ pageTitle }}{% endblock %}

{% block extra_css %}
<style>
    .streamlined-card {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .compact-form .form-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #495057;
    }
    
    .compact-form .form-control, 
    .compact-form .form-select {
        border-radius: 6px;
        border: 1px solid #ced4da;
        padding: 0.5rem 0.75rem;
    }
    
    .compact-form .form-control:focus, 
    .compact-form .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .btn-search {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border: none;
        padding: 0.75rem 2rem;
        font-weight: 600;
        border-radius: 6px;
        transition: all 0.3s ease;
    }
    
    .btn-search:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }
    
    .advanced-filters {
        background-color: #f8f9fa;
        border-radius: 6px;
        padding: 1.5rem;
        margin-top: 1rem;
    }
    
    .filter-toggle {
        cursor: pointer;
        color: #007bff;
        text-decoration: none;
        font-weight: 600;
    }
    
    .filter-toggle:hover {
        color: #0056b3;
        text-decoration: underline;
    }
    
    .info-section {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-radius: 0 6px 6px 0;
    }
    
    .info-section h6 {
        color: #1976d2;
        margin-bottom: 0.5rem;
    }
    
    .info-section p {
        margin-bottom: 0;
        color: #424242;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row justify-content-center">
        <div class="col-12 col-xl-10">
            <!-- Main Search Form -->
            <div class="card streamlined-card compact-form">
                <div class="card-header">
                    <h1 class="h4 mb-0">
                        <i class="fas fa-search-plus me-2"></i>
                        Căutare în Masă - Portal Dosare Judecătorești
                    </h1>
                </div>
                <div class="card-body">
                    <form method="POST" action="/search/bulk" id="bulkSearchForm">
                        <div class="row">
                            <div class="col-md-8">
                                <label for="bulkSearchTerms" class="form-label">
                                    <i class="fas fa-list me-1"></i>
                                    Termeni de căutare
                                </label>
                                <textarea
                                    class="form-control"
                                    id="bulkSearchTerms"
                                    name="bulkSearchTerms"
                                    rows="8"
                                    placeholder="Introduceți termenii de căutare (câte unul pe linie):&#10;&#10;Exemple:&#10;1234/2023&#10;Ion Popescu&#10;divorț&#10;executare silită&#10;SC COMPANY SRL"
                                ></textarea>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Sistemul detectează automat tipul de căutare pentru fiecare termen. Maxim 100 de termeni.
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary btn-search">
                                        <i class="fas fa-search me-2"></i>
                                        Căutare în Masă
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="clearForm()">
                                        <i class="fas fa-eraser me-2"></i>
                                        Șterge Tot
                                    </button>
                                </div>
                                
                                <!-- Quick Actions -->
                                <div class="mt-3">
                                    <h6 class="text-muted">Acțiuni Rapide:</h6>
                                    <div class="d-grid gap-1">
                                        <button type="button" class="btn btn-sm btn-outline-info" onclick="addSampleData()">
                                            <i class="fas fa-plus me-1"></i>
                                            Adaugă Exemple
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-warning" onclick="toggleAdvancedFilters()">
                                            <i class="fas fa-filter me-1"></i>
                                            Filtre Avansate
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Filters Section (Hidden by default) -->
                        <div id="advancedFilters" class="advanced-filters" style="display: none;">
                            <h6 class="mb-3">
                                <i class="fas fa-sliders-h me-2"></i>
                                Filtre Avansate
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="institutie" class="form-label">Instituție</label>
                                    <select class="form-select" id="institutie" name="institutie">
                                        <option value="">Toate instituțiile</option>
                                        {% for cod, nume in instante %}
                                            <option value="{{ cod }}">{{ nume }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="categorieInstanta" class="form-label">Categorie Instanță</label>
                                    <select class="form-select" id="categorieInstanta" name="categorieInstanta">
                                        <option value="">Toate categoriile</option>
                                        <option value="CurteadeApel">Curte de Apel</option>
                                        <option value="Tribunalul">Tribunal</option>
                                        <option value="JudecatoriaOras">Judecătoria Oraș</option>
                                        <option value="JudecatoriaSector">Judecătoria Sector</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label for="categorieCaz" class="form-label">Categorie Caz</label>
                                    <select class="form-select" id="categorieCaz" name="categorieCaz">
                                        <option value="">Toate categoriile</option>
                                        <option value="civil">Civil</option>
                                        <option value="penal">Penal</option>
                                        <option value="comercial">Comercial</option>
                                        <option value="contencios">Contencios Administrativ</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="dataInceput" class="form-label">Data Început</label>
                                    <input type="text" class="form-control datepicker" id="dataInceput" name="dataInceput" placeholder="DD.MM.YYYY">
                                </div>
                                <div class="col-md-3">
                                    <label for="dataSfarsit" class="form-label">Data Sfârșit</label>
                                    <input type="text" class="form-control datepicker" id="dataSfarsit" name="dataSfarsit" placeholder="DD.MM.YYYY">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Information Section -->
            <div class="info-section mt-4">
                <h6><i class="fas fa-info-circle me-2"></i>Informații despre Portal</h6>
                <p>
                    Acest portal oferă acces la informațiile publice din dosarele judecătorești din România. 
                    Datele sunt actualizate în timp real și provin din sistemul oficial al instanțelor de judecată.
                    Pentru rezultate optime, folosiți termeni de căutare specifici și filtrele avansate.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function clearForm() {
    document.getElementById('bulkSearchTerms').value = '';
    document.getElementById('institutie').value = '';
    document.getElementById('categorieInstanta').value = '';
    document.getElementById('categorieCaz').value = '';
    document.getElementById('dataInceput').value = '';
    document.getElementById('dataSfarsit').value = '';
}

function addSampleData() {
    const sampleData = `1234/2023
Ion Popescu
divorț
executare silită
SC COMPANY SRL`;
    document.getElementById('bulkSearchTerms').value = sampleData;
}

function toggleAdvancedFilters() {
    const filtersDiv = document.getElementById('advancedFilters');
    if (filtersDiv.style.display === 'none') {
        filtersDiv.style.display = 'block';
    } else {
        filtersDiv.style.display = 'none';
    }
}

// Initialize date pickers if available
document.addEventListener('DOMContentLoaded', function() {
    if (typeof flatpickr !== 'undefined') {
        flatpickr('.datepicker', {
            dateFormat: 'd.m.Y',
            locale: 'ro',
            allowInput: true,
            disableMobile: true
        });
    }
});
</script>
{% endblock %}
