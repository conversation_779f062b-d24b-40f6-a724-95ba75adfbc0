<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
/**
 * Pagina de căutare a ședințelor de judecată
 * Portal Judiciar - Căutare Ședințe
 */

// Includem bootstrap-ul pentru noua structură
require_once 'bootstrap.php';

// CRITICAL FIX: Verificăm IMEDIAT dacă este o cerere de export
// TREBUIE să fie PRIMUL lucru pentru a evita contaminarea cu HTML
if (isset($_GET['export']) && isset($_GET['session_results']) && $_GET['session_results'] === '1') {
    // Procesăm exportul în funcție de tip și ieșim IMEDIAT
    if ($_GET['export'] === 'xlsx') {
        handleSessionExcelExportOnly();
    } elseif ($_GET['export'] === 'pdf') {
        handleSessionPdfExportOnly();
    } elseif ($_GET['export'] === 'txt') {
        handleSessionTxtExportOnly();
    } else {
        // Default to TXT for backward compatibility
        handleSessionTxtExportOnly();
    }
    exit; // STOP - nu mai executăm nimic altceva
}

// Inițializare variabile
$results = [];
$error = null;
$searchParams = [];
$totalResults = 0;
$hasSearchCriteria = false;

// Obținem lista instanțelor pentru dropdown
$institutii = getInstanteList();

// Procesare parametri de intrare
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $dataSedinta = isset($_POST['dataSedinta']) ? trim($_POST['dataSedinta']) : '';
    $institutie = isset($_POST['institutie']) && $_POST['institutie'] !== '' ? $_POST['institutie'] : null;

    // Verificare dacă s-a trimis cel puțin un criteriu de căutare
    $hasSearchCriteria = !empty($dataSedinta) || !empty($institutie);

    if ($hasSearchCriteria) {
        // Validare dată
        $dateValidation = validateRomanianDate($dataSedinta);
        if (!$dateValidation['valid']) {
            $error = $dateValidation['error'];
        } else {
            try {
                // Inițializăm serviciul de ședințe
                $sedinteService = new SedinteService();

                // Construim parametrii de căutare
                $searchParams = [
                    'dataSedinta' => $dateValidation['date'] . 'T00:00:00',
                    'institutie' => $institutie
                ];

                // Efectuăm căutarea și detectăm dacă s-a folosit fallback
                $originalInstitutie = $institutie;
                $results = $sedinteService->cautareSedinte($searchParams);
                $totalResults = count($results);

                // Verificăm dacă s-a folosit fallback pentru instituție
                // Aceasta se întâmplă când API-ul SOAP nu acceptă codul instituției
                // și căutarea se face fără filtrul de instituție, apoi se filtrează local
                if (!empty($originalInstitutie)) {
                    // Testăm dacă codul instituției este problematic prin verificarea logurilor
                    $logFile = __DIR__ . '/logs/soap_sessions.log';
                    if (file_exists($logFile)) {
                        $recentLogs = file_get_contents($logFile);
                        if (strpos($recentLogs, "Institution code '{$originalInstitutie}' not valid for SOAP API") !== false) {
                            // S-a folosit fallback - afișăm notificare
                            $institutii = getInstanteList();
                            $institutionName = $institutii[$originalInstitutie] ?? $originalInstitutie;
                            $error = "Notă: Codul instituției '{$institutionName}' nu este recunoscut de API-ul SOAP pentru ședințe. Căutarea a fost efectuată fără filtrul de instituție și rezultatele au fost filtrate local.";
                        }
                    }
                }

            } catch (Exception $e) {
                $error = 'Eroare la căutarea ședințelor: ' . $e->getMessage();
                error_log('Eroare căutare ședințe: ' . $e->getMessage());
            }
        }
    } else {
        $error = 'Vă rugăm să introduceți cel puțin un criteriu de căutare (data ședinței sau instituția).';
    }
}

/**
 * Validează data în format românesc DD.MM.YYYY
 */
function validateRomanianDate($dateString) {
    if (empty($dateString)) {
        return ['valid' => false, 'date' => '', 'error' => 'Data ședinței este obligatorie.'];
    }

    // Verificăm formatul DD.MM.YYYY
    if (!preg_match('/^(\d{1,2})\.(\d{1,2})\.(\d{4})$/', $dateString, $matches)) {
        return ['valid' => false, 'date' => '', 'error' => 'Formatul datei trebuie să fie ZZ.LL.AAAA (ex: 15.03.2023)'];
    }

    $day = (int)$matches[1];
    $month = (int)$matches[2];
    $year = (int)$matches[3];

    // Verificăm validitatea datei
    if (!checkdate($month, $day, $year)) {
        return ['valid' => false, 'date' => '', 'error' => 'Data introdusă nu este validă'];
    }

    // Verificăm limitele anului (nu acceptăm date prea vechi sau prea în viitor)
    $currentYear = (int)date('Y');
    if ($year < 1990 || $year > $currentYear + 5) {
        return ['valid' => false, 'date' => '', 'error' => "Anul trebuie să fie între 1990 și " . ($currentYear + 5)];
    }

    // Convertim la format YYYY-MM-DD pentru SOAP API
    $formattedDate = sprintf('%04d-%02d-%02d', $year, $month, $day);

    return ['valid' => true, 'date' => $formattedDate, 'error' => ''];
}

/**
 * DEDICATED TXT EXPORT FUNCTION - NO HTML CONTAMINATION
 */
function handleSessionTxtExportOnly() {
    try {
        // Setăm encoding-ul pentru PHP
        if (function_exists('mb_internal_encoding')) {
            mb_internal_encoding('UTF-8');
        }

        // Procesăm parametrii de căutare pentru API
        $dataSedinta = $_GET['dataSedinta'] ?? '';
        $institutie = $_GET['institutie'] ?? '';

        // Validare dată
        $dateValidation = validateRomanianDate($dataSedinta);
        if (!$dateValidation['valid']) {
            http_response_code(400);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => $dateValidation['error']], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Inițializăm serviciul de ședințe
        $sedinteService = new SedinteService();

        // Construim parametrii de căutare
        $searchParams = [
            'dataSedinta' => $dateValidation['date'] . 'T00:00:00',
            'institutie' => $institutie ?: null
        ];

        // Obținem rezultatele pentru export
        $allResults = $sedinteService->cautareSedinte($searchParams);

        // Validăm că avem rezultate
        if (empty($allResults)) {
            // Returnăm o pagină HTML cu mesaj de eroare în loc de JSON
            header('Content-Type: text/html; charset=UTF-8');
            echo '<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eroare Export - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="alert alert-warning">
            <h4><i class="fas fa-exclamation-triangle"></i> Nu există rezultate pentru export</h4>
            <p>Nu au fost găsite ședințe pentru criteriile de căutare specificate.</p>
            <a href="sedinte.php" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> Înapoi la căutare
            </a>
        </div>
    </div>
</body>
</html>';
            return;
        }

        // Generăm numele fișierului
        $filename = 'Sedinte_' . str_replace('.', '-', $dataSedinta) . '_' . date('Y-m-d_H-i-s') . '.txt';

        // CRITICAL: Curățăm TOATE buffer-ele de output
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Setăm header-ele pentru TXT CURAT
        header('Content-Type: text/plain; charset=UTF-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"; filename*=UTF-8\'\'' . rawurlencode($filename));
        header('Content-Transfer-Encoding: 8bit');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        header('X-Content-Type-Options: nosniff');
        header('Content-Description: File Transfer');

        // Generăm DOAR conținutul TXT cu informații despre instituție
        generateSessionTxtContent($allResults, $institutie);

    } catch (Exception $e) {
        // În caz de eroare, returnăm JSON curat
        http_response_code(500);
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(['error' => 'Eroare la generarea TXT: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * DEDICATED EXCEL EXPORT FUNCTION - TRUE XLSX FORMAT
 */
function handleSessionExcelExportOnly() {
    try {
        // Setăm encoding-ul pentru PHP
        if (function_exists('mb_internal_encoding')) {
            mb_internal_encoding('UTF-8');
        }

        // Procesăm parametrii de căutare pentru API
        $dataSedinta = $_GET['dataSedinta'] ?? '';
        $institutie = $_GET['institutie'] ?? '';

        // Validare dată
        $dateValidation = validateRomanianDate($dataSedinta);
        if (!$dateValidation['valid']) {
            http_response_code(400);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => $dateValidation['error']], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Inițializăm serviciul de ședințe
        $sedinteService = new SedinteService();

        // Construim parametrii de căutare
        $searchParams = [
            'dataSedinta' => $dateValidation['date'] . 'T00:00:00',
            'institutie' => $institutie ?: null
        ];

        // Obținem rezultatele pentru export
        $allResults = $sedinteService->cautareSedinte($searchParams);

        // Validăm că avem rezultate
        if (empty($allResults)) {
            // Returnăm o pagină HTML cu mesaj de eroare în loc de JSON
            header('Content-Type: text/html; charset=UTF-8');
            echo '<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eroare Export Excel - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="alert alert-warning">
            <h4><i class="fas fa-exclamation-triangle"></i> Nu există rezultate pentru export Excel</h4>
            <p>Nu au fost găsite ședințe pentru criteriile de căutare specificate.</p>
            <a href="sedinte.php" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> Înapoi la căutare
            </a>
        </div>
    </div>
</body>
</html>';
            return;
        }

        // Generăm numele fișierului în format Excel
        $filename = 'Sedinte_' . str_replace('.', '-', $dataSedinta) . '_' . date('Y-m-d_H-i-s') . '.xlsx';

        // CRITICAL: Curățăm TOATE buffer-ele de output
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Generăm fișierul Excel cu informații despre instituție
        generateSessionExcelFile($allResults, $filename, $institutie);

    } catch (Exception $e) {
        // În caz de eroare, returnăm JSON curat
        http_response_code(500);
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(['error' => 'Eroare la generarea Excel: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * DEDICATED PDF EXPORT FUNCTION - TCPDF INTEGRATION
 */
function handleSessionPdfExportOnly() {
    try {
        // Setăm encoding-ul pentru PHP
        if (function_exists('mb_internal_encoding')) {
            mb_internal_encoding('UTF-8');
        }

        // Procesăm parametrii de căutare pentru API
        $dataSedinta = $_GET['dataSedinta'] ?? '';
        $institutie = $_GET['institutie'] ?? '';

        // Validare dată
        $dateValidation = validateRomanianDate($dataSedinta);
        if (!$dateValidation['valid']) {
            http_response_code(400);
            header('Content-Type: application/json; charset=UTF-8');
            echo json_encode(['error' => $dateValidation['error']], JSON_UNESCAPED_UNICODE);
            return;
        }

        // Inițializăm serviciul de ședințe
        $sedinteService = new SedinteService();

        // Construim parametrii de căutare
        $searchParams = [
            'dataSedinta' => $dateValidation['date'] . 'T00:00:00',
            'institutie' => $institutie ?: null
        ];

        // Obținem rezultatele pentru export
        $allResults = $sedinteService->cautareSedinte($searchParams);

        if (empty($allResults)) {
            // Returnăm o pagină HTML de eroare dacă nu avem rezultate
            echo '<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export PDF - Nu există rezultate</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-file-pdf text-danger mb-3" style="font-size: 3rem;"></i>
                        <h4 class="card-title">Nu există rezultate pentru export</h4>
                        <p class="card-text">Nu au fost găsite ședințe pentru criteriile specificate.</p>
                        <a href="sedinte.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> Înapoi la căutare
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>';
            return;
        }

        // Generăm numele fișierului în format PDF
        $filename = 'Sedinte_' . str_replace('.', '-', $dataSedinta) . '_' . date('Y-m-d_H-i-s') . '.pdf';

        // CRITICAL: Curățăm TOATE buffer-ele de output
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Generăm fișierul PDF cu informații despre instituție
        generateSessionPdfFile($allResults, $filename, $institutie, $dataSedinta);

    } catch (Exception $e) {
        // În caz de eroare, returnăm JSON curat
        http_response_code(500);
        header('Content-Type: application/json; charset=UTF-8');
        echo json_encode(['error' => 'Eroare la generarea PDF: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}

/**
 * Generează conținutul TXT pentru export ședințe cu suport complet UTF-8
 */
function generateSessionTxtContent($results, $institutieParam = null) {
    // Asigurăm encoding-ul UTF-8
    mb_internal_encoding('UTF-8');

    // Obținem lista instituțiilor pentru mapare
    $institutii = getInstanteList();

    // Header pentru fișierul TXT cu caractere românești
    $content = "";
    $content .= "ȘEDINȚE DE JUDECATĂ - REZULTATE CĂUTARE\n";
    $content .= "═══════════════════════════════════════════════════════════════════════════════\n";
    $content .= "Generat la: " . date('d.m.Y H:i:s') . "\n";
    $content .= "Total ședințe găsite: " . count($results) . "\n";

    // Adăugăm informații despre instituția căutată
    if (!empty($institutieParam)) {
        $numeInstitutie = $institutii[$institutieParam] ?? $institutieParam;
        $content .= "Instituție căutată: " . $numeInstitutie . "\n";
    } else {
        $content .= "Instituție căutată: Toate instituțiile\n";
    }

    $content .= "═══════════════════════════════════════════════════════════════════════════════\n\n";

    foreach ($results as $index => $sedinta) {
        $content .= "┌─ ȘEDINȚA " . ($index + 1) . " " . str_repeat("─", 60) . "\n";
        $content .= "│\n";

        // Determinăm instituția pentru această ședință
        $institutieNume = 'N/A';
        if (!empty($institutieParam)) {
            // Dacă avem parametrul de căutare, folosim acela
            $institutieNume = $institutii[$institutieParam] ?? $institutieParam;
        } elseif (!empty($sedinta->dosare)) {
            // Altfel, încercăm să găsim instituția din primul dosar
            foreach ($sedinta->dosare as $dosar) {
                if ($dosar && !empty($dosar->institutie)) {
                    $institutieNume = $institutii[$dosar->institutie] ?? $dosar->institutie;
                    break;
                }
            }
        }

        $content .= "│ 🏛️  Instituție: " . $institutieNume . "\n";
        $content .= "│ 🏢  Departament: " . ($sedinta->departament ?? 'N/A') . "\n";

        // Formatăm informațiile despre judecători pentru export
        $completText = $sedinta->complet ?? 'N/A';
        if ($completText !== 'N/A') {
            $judgeInfo = parseJudgeInformation($completText);
            $formattedJudgeInfo = formatJudgeInformationForExport($judgeInfo);
            $content .= "│ 👥  Complet: " . $formattedJudgeInfo . "\n";
        } else {
            $content .= "│ 👥  Complet: N/A\n";
        }

        $content .= "│ 📅  Data: " . ($sedinta->data ?? 'N/A') . "\n";
        $content .= "│ 🕐  Ora: " . ($sedinta->ora ?? 'N/A') . "\n";
        $content .= "│\n";

        if (!empty($sedinta->dosare)) {
            $content .= "│ 📁  Dosare programate:\n";
            foreach ($sedinta->dosare as $dosar) {
                if ($dosar && !empty($dosar->numar)) {
                    $content .= "│     • " . $dosar->numar;
                    // Adăugăm instituția dosarului dacă diferă de cea principală
                    if (!empty($dosar->institutie) && $dosar->institutie !== $institutieParam) {
                        $dosarInstitutie = $institutii[$dosar->institutie] ?? $dosar->institutie;
                        if ($dosarInstitutie !== $institutieNume) {
                            $content .= " (" . $dosarInstitutie . ")";
                        }
                    }
                    $content .= "\n";
                }
            }
        } else {
            $content .= "│ ℹ️   Nu sunt dosare programate pentru această ședință\n";
        }

        $content .= "│\n";
        $content .= "└" . str_repeat("─", 70) . "\n\n";
    }

    $content .= "═══════════════════════════════════════════════════════════════════════════════\n";
    $content .= "Exportat din Portal Judiciar - DosareJust.ro\n";
    $content .= "Pentru informații oficiale, consultați portal.just.ro\n";
    $content .= "═══════════════════════════════════════════════════════════════════════════════\n";

    // Asigurăm că outputul este în UTF-8
    echo mb_convert_encoding($content, 'UTF-8', 'UTF-8');
}

/**
 * Generează fișierul Excel pentru export ședințe folosind PhpSpreadsheet
 */
function generateSessionExcelFile($results, $filename, $institutieParam = null) {
    require_once __DIR__ . '/vendor/autoload.php';

    try {
        // Obținem lista instituțiilor pentru mapare
        $institutii = getInstanteList();

        // Creăm un nou spreadsheet
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Setăm titlul foii
        $sheet->setTitle('Ședințe de Judecată');

        // Header-ul principal
        $sheet->setCellValue('A1', 'ȘEDINȚE DE JUDECATĂ - REZULTATE CĂUTARE');
        $sheet->mergeCells('A1:G1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('A1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('007BFF');
        $sheet->getStyle('A1')->getFont()->getColor()->setRGB('FFFFFF');

        // Informații despre export
        $sheet->setCellValue('A2', 'Generat la: ' . date('d.m.Y H:i:s'));
        $sheet->setCellValue('A3', 'Total ședințe găsite: ' . count($results));

        // Adăugăm informații despre instituția căutată
        if (!empty($institutieParam)) {
            $numeInstitutie = $institutii[$institutieParam] ?? $institutieParam;
            $sheet->setCellValue('A4', 'Instituție căutată: ' . $numeInstitutie);
        } else {
            $sheet->setCellValue('A4', 'Instituție căutată: Toate instituțiile');
        }

        $sheet->getStyle('A2:A4')->getFont()->setItalic(true);

        // Header-ul tabelului
        $headerRow = 6;
        $headers = ['Nr. Ședință', 'Instituție', 'Departament', 'Complet', 'Data', 'Ora', 'Dosare Programate'];
        $column = 'A';

        foreach ($headers as $header) {
            $sheet->setCellValue($column . $headerRow, $header);
            $sheet->getStyle($column . $headerRow)->getFont()->setBold(true);
            $sheet->getStyle($column . $headerRow)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('F8F9FA');
            $column++;
        }

        // Datele ședințelor
        $row = $headerRow + 1;
        foreach ($results as $index => $sedinta) {
            $sheet->setCellValue('A' . $row, $index + 1);

            // Determinăm instituția pentru această ședință
            $institutieNume = 'N/A';
            if (!empty($institutieParam)) {
                // Dacă avem parametrul de căutare, folosim acela
                $institutieNume = $institutii[$institutieParam] ?? $institutieParam;
            } elseif (!empty($sedinta->dosare)) {
                // Altfel, încercăm să găsim instituția din primul dosar
                foreach ($sedinta->dosare as $dosar) {
                    if ($dosar && !empty($dosar->institutie)) {
                        $institutieNume = $institutii[$dosar->institutie] ?? $dosar->institutie;
                        break;
                    }
                }
            }

            $sheet->setCellValue('B' . $row, $institutieNume);
            $sheet->setCellValue('C' . $row, $sedinta->departament ?? 'N/A');

            // Formatăm informațiile despre judecători pentru export Excel
            $completText = $sedinta->complet ?? 'N/A';
            if ($completText !== 'N/A') {
                $judgeInfo = parseJudgeInformation($completText);
                $formattedJudgeInfo = formatJudgeInformationForExport($judgeInfo);
                $sheet->setCellValue('D' . $row, $formattedJudgeInfo);
            } else {
                $sheet->setCellValue('D' . $row, 'N/A');
            }

            $sheet->setCellValue('E' . $row, $sedinta->data ?? 'N/A');
            $sheet->setCellValue('F' . $row, $sedinta->ora ?? 'N/A');

            // Dosarele programate cu informații despre instituție
            $dosareList = '';
            if (!empty($sedinta->dosare)) {
                $dosareNumbers = [];
                foreach ($sedinta->dosare as $dosar) {
                    if ($dosar && !empty($dosar->numar)) {
                        $dosarText = $dosar->numar;
                        // Adăugăm instituția dosarului dacă diferă de cea principală
                        if (!empty($dosar->institutie) && $dosar->institutie !== $institutieParam) {
                            $dosarInstitutie = $institutii[$dosar->institutie] ?? $dosar->institutie;
                            if ($dosarInstitutie !== $institutieNume) {
                                $dosarText .= " (" . $dosarInstitutie . ")";
                            }
                        }
                        $dosareNumbers[] = $dosarText;
                    }
                }
                $dosareList = implode('; ', $dosareNumbers);
            } else {
                $dosareList = 'Nu sunt dosare programate';
            }
            $sheet->setCellValue('G' . $row, $dosareList);

            $row++;
        }

        // Ajustăm lățimea coloanelor
        foreach (range('A', 'G') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Setăm header-ele pentru download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"; filename*=UTF-8\'\'' . rawurlencode($filename));
        header('Cache-Control: max-age=0');
        header('Cache-Control: max-age=1');
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('Cache-Control: cache, must-revalidate');
        header('Pragma: public');

        // Generăm și trimitem fișierul
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save('php://output');

    } catch (Exception $e) {
        // În caz de eroare, generăm un CSV simplu
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        echo "Departament,Complet,Data,Ora,Dosare\n";

        foreach ($results as $sedinta) {
            $dosareList = '';
            if (!empty($sedinta->dosare)) {
                $dosareNumbers = [];
                foreach ($sedinta->dosare as $dosar) {
                    if ($dosar && !empty($dosar->numar)) {
                        $dosareNumbers[] = $dosar->numar;
                    }
                }
                $dosareList = implode('; ', $dosareNumbers);
            }

            // Formatăm informațiile despre judecători pentru CSV fallback
            $completText = $sedinta->complet ?? '';
            if (!empty($completText)) {
                $judgeInfo = parseJudgeInformation($completText);
                $formattedJudgeInfo = formatJudgeInformationForExport($judgeInfo);
            } else {
                $formattedJudgeInfo = '';
            }

            echo '"' . ($sedinta->departament ?? '') . '",';
            echo '"' . $formattedJudgeInfo . '",';
            echo '"' . ($sedinta->data ?? '') . '",';
            echo '"' . ($sedinta->ora ?? '') . '",';
            echo '"' . $dosareList . '"' . "\n";
        }
    }
}

/**
 * Generează fișierul PDF pentru export ședințe folosind TCPDF
 */
function generateSessionPdfFile($results, $filename, $institutieParam = null, $dataSedinta = '') {
    // Configurare TCPDF
    define('K_TCPDF_EXTERNAL_CONFIG', true);

    // Verificăm dacă TCPDF este disponibil
    $tcpdfPath = __DIR__ . '/vendor/tecnickcom/tcpdf/tcpdf.php';
    if (!file_exists($tcpdfPath)) {
        // Încercăm calea alternativă
        $tcpdfPath = dirname(__DIR__) . '/vendor/tecnickcom/tcpdf/tcpdf.php';
        if (!file_exists($tcpdfPath)) {
            throw new Exception("TCPDF nu este instalat sau calea este incorectă. Căutat în: " . $tcpdfPath);
        }
    }

    // Includem TCPDF
    require_once $tcpdfPath;

    try {
        // Obținem lista instituțiilor pentru mapare
        $institutii = getInstanteList();

        // Creăm o instanță TCPDF
        $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);

        // Setăm informațiile documentului
        $pdf->SetCreator('Portal Judiciar');
        $pdf->SetAuthor('Portal Judiciar');
        $pdf->SetTitle('Ședințe de Judecată - ' . $dataSedinta);
        $pdf->SetSubject('Rezultate căutare ședințe judecată');
        $pdf->SetKeywords('ședințe, judecată, instanță, România');

        // Setăm marginile (2cm = 20mm)
        $pdf->SetMargins(20, 20, 20);
        $pdf->SetHeaderMargin(10);
        $pdf->SetFooterMargin(15);

        // Setăm auto page breaks
        $pdf->SetAutoPageBreak(TRUE, 25);

        // Eliminăm header și footer default
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);

        // Adăugăm prima pagină
        $pdf->AddPage();

        // Setăm fontul principal
        $pdf->SetFont('dejavusans', '', 10);

        // Header principal
        $pdf->SetFont('dejavusans', 'B', 16);
        $pdf->SetTextColor(0, 123, 255); // Blue color #007bff
        $pdf->Cell(0, 15, 'ȘEDINȚE DE JUDECATĂ - REZULTATE CĂUTARE', 0, 1, 'C');

        $pdf->Ln(5);

        // Informații despre export
        $pdf->SetFont('dejavusans', '', 10);
        $pdf->SetTextColor(0, 0, 0);
        $pdf->Cell(0, 6, 'Generat la: ' . date('d.m.Y H:i:s'), 0, 1, 'L');
        $pdf->Cell(0, 6, 'Data căutată: ' . $dataSedinta, 0, 1, 'L');

        // Informații despre instituția căutată
        if (!empty($institutieParam)) {
            $numeInstitutie = $institutii[$institutieParam] ?? $institutieParam;
            $pdf->Cell(0, 6, 'Instituție: ' . $numeInstitutie, 0, 1, 'L');
        } else {
            $pdf->Cell(0, 6, 'Instituție: Toate instituțiile', 0, 1, 'L');
        }

        $pdf->Cell(0, 6, 'Total ședințe găsite: ' . count($results), 0, 1, 'L');

        $pdf->Ln(10);

        // Continuăm cu tabelul în următoarea funcție pentru a respecta limita de 150 linii
        generateSessionPdfTable($pdf, $results, $institutii, $institutieParam);

        // Setăm header-ele pentru download
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"; filename*=UTF-8\'\'' . rawurlencode($filename));
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        header('X-Content-Type-Options: nosniff');
        header('Content-Description: File Transfer');

        // Generăm și trimitem PDF-ul
        $pdf->Output($filename, 'D');

    } catch (Exception $e) {
        throw new Exception("Eroare la generarea PDF: " . $e->getMessage());
    }
}

/**
 * Generează tabelul cu ședințele în PDF
 */
function generateSessionPdfTable($pdf, $results, $institutii, $institutieParam) {
    // Header tabel
    $pdf->SetFont('dejavusans', 'B', 9);
    $pdf->SetFillColor(0, 123, 255); // Blue background #007bff
    $pdf->SetTextColor(255, 255, 255); // White text

    // Definim lățimile coloanelor optimizate (total 170mm pentru A4 cu margini de 20mm)
    // Ajustăm pentru a preveni overflow-ul de text
    $colWidths = [12, 22, 30, 40, 66]; // Nr, Data/Ora, Departament, Complet, Dosare

    // Header-ul tabelului
    $pdf->Cell($colWidths[0], 8, 'Nr.', 1, 0, 'C', true);
    $pdf->Cell($colWidths[1], 8, 'Data/Ora', 1, 0, 'C', true);
    $pdf->Cell($colWidths[2], 8, 'Departament', 1, 0, 'C', true);
    $pdf->Cell($colWidths[3], 8, 'Complet', 1, 0, 'C', true);
    $pdf->Cell($colWidths[4], 8, 'Dosare Programate', 1, 1, 'C', true);

    // Resetăm culoarea textului pentru conținut
    $pdf->SetTextColor(0, 0, 0);
    $pdf->SetFont('dejavusans', '', 8);

    // Datele ședințelor
    foreach ($results as $index => $sedinta) {
        // Preparăm datele pentru această ședință
        $nr = $index + 1;
        $dataOra = ($sedinta->data ?? 'N/A') . "\n" . ($sedinta->ora ?? 'N/A');
        $departament = $sedinta->departament ?? 'N/A';

        // Formatăm informațiile despre judecători pentru export PDF
        $completText = $sedinta->complet ?? 'N/A';
        if ($completText !== 'N/A') {
            $judgeInfo = parseJudgeInformation($completText);
            $complet = formatJudgeInformationForExport($judgeInfo);
        } else {
            $complet = 'N/A';
        }

        // Preparăm lista dosarelor
        $dosareText = '';
        if (!empty($sedinta->dosare)) {
            $dosareNumbers = [];
            foreach ($sedinta->dosare as $dosar) {
                if ($dosar && !empty($dosar->numar)) {
                    $dosarText = $dosar->numar;
                    // Adăugăm instituția dosarului dacă diferă de cea principală
                    if (!empty($dosar->institutie) && $dosar->institutie !== $institutieParam) {
                        $dosarInstitutie = $institutii[$dosar->institutie] ?? $dosar->institutie;
                        // Determinăm instituția principală pentru comparație
                        $institutieNume = 'N/A';
                        if (!empty($institutieParam)) {
                            $institutieNume = $institutii[$institutieParam] ?? $institutieParam;
                        }
                        if ($dosarInstitutie !== $institutieNume) {
                            $dosarText .= " (" . $dosarInstitutie . ")";
                        }
                    }
                    $dosareNumbers[] = $dosarText;
                }
            }
            $dosareText = implode('; ', $dosareNumbers);
        } else {
            $dosareText = 'Nu sunt dosare programate';
        }

        // Calculăm înălțimea necesară pentru fiecare coloană cu text multi-linie
        $minHeight = 8;  // Înălțimea minimă pentru o celulă

        // Calculăm înălțimea pentru fiecare coloană folosind getStringHeight
        $dataOraHeight = $pdf->getStringHeight($colWidths[1], $dataOra);
        $departamentHeight = $pdf->getStringHeight($colWidths[2], $departament);
        $completHeight = $pdf->getStringHeight($colWidths[3], $complet);
        $dosareHeight = $pdf->getStringHeight($colWidths[4], $dosareText);

        // Înălțimea maximă necesară (cu padding suplimentar pentru alinierea corectă)
        $maxHeight = max($minHeight, $dataOraHeight + 2, $departamentHeight + 2, $completHeight + 2, $dosareHeight + 2);

        // Verificăm dacă avem loc pe pagină
        if ($pdf->GetY() + $maxHeight > $pdf->getPageHeight() - 25) {
            $pdf->AddPage();
        }

        // Salvăm poziția Y curentă
        $startY = $pdf->GetY();
        $startX = $pdf->GetX();

        // Metoda îmbunătățită: folosim Rect pentru contur și MultiCell cu poziționare precisă

        // 1. Desenăm conturul tabelului folosind Rect pentru control total
        $pdf->SetDrawColor(0, 0, 0); // Culoare neagră pentru contur
        $pdf->SetLineWidth(0.2);

        // Desenăm conturul fiecărei celule
        $currentX = $startX;
        for ($i = 0; $i < 5; $i++) {
            $pdf->Rect($currentX, $startY, $colWidths[$i], $maxHeight);
            $currentX += $colWidths[$i];
        }

        // 2. Adăugăm conținutul cu alinierea corectă în fiecare celulă

        // Celula Nr - centrat vertical și orizontal
        $cellY = $startY + ($maxHeight - 4) / 2; // Centrare verticală pentru text simplu
        $pdf->SetXY($startX, $cellY);
        $pdf->Cell($colWidths[0], 4, $nr, 0, 0, 'C');

        // Celula Data/Ora - centrat orizontal, centrat vertical
        $cellX = $startX + $colWidths[0];
        $cellY = $startY + ($maxHeight - $dataOraHeight) / 2;
        $pdf->SetXY($cellX + 1, $cellY); // +1 pentru padding
        $pdf->MultiCell($colWidths[1] - 2, 4, $dataOra, 0, 'C', false, 0);

        // Celula Departament - aliniat stânga, centrat vertical
        $cellX = $startX + $colWidths[0] + $colWidths[1];
        $cellY = $startY + ($maxHeight - $departamentHeight) / 2;
        $pdf->SetXY($cellX + 1, $cellY);
        $pdf->MultiCell($colWidths[2] - 2, 4, $departament, 0, 'L', false, 0);

        // Celula Complet - aliniat stânga, centrat vertical
        $cellX = $startX + $colWidths[0] + $colWidths[1] + $colWidths[2];
        $cellY = $startY + ($maxHeight - $completHeight) / 2;
        $pdf->SetXY($cellX + 1, $cellY);
        $pdf->MultiCell($colWidths[3] - 2, 4, $complet, 0, 'L', false, 0);

        // Celula Dosare - aliniat stânga, aliniat sus cu padding
        $cellX = $startX + $colWidths[0] + $colWidths[1] + $colWidths[2] + $colWidths[3];
        $cellY = $startY + 1; // Padding de sus pentru liste
        $pdf->SetXY($cellX + 1, $cellY);
        $pdf->MultiCell($colWidths[4] - 2, 4, $dosareText, 0, 'L', false, 0);

        // Setăm poziția pentru următoarea linie
        $pdf->SetXY($startX, $startY + $maxHeight);
    }

    // Footer
    $pdf->Ln(10);
    $pdf->SetFont('dejavusans', 'I', 8);
    $pdf->SetTextColor(108, 117, 125); // Gray color
    $pdf->Cell(0, 6, 'Exportat din Portal Judiciar - DosareJust.ro', 0, 1, 'C');
    $pdf->Cell(0, 6, 'Pentru informații oficiale, consultați portal.just.ro', 0, 1, 'C');

    // Numerotare pagini
    $pdf->SetY(-15);
    $pdf->SetFont('dejavusans', '', 8);
    $pdf->Cell(0, 10, 'Pagina ' . $pdf->getAliasNumPage() . ' din ' . $pdf->getAliasNbPages(), 0, 0, 'C');
}
?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Căutare Ședințe - Portal Judiciar</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .search-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 0.75rem 2rem;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        .results-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }
        .case-link {
            color: #667eea;
            text-decoration: none;
        }
        .case-link:hover {
            color: #5a6fd8;
            text-decoration: underline;
        }
        .export-buttons {
            margin-top: 1rem;
        }
        .export-btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-gavel"></i> Portal Judiciar</h1>
                    <p class="mb-0">Căutare Ședințe de Judecată</p>
                </div>
                <div class="col-md-4 text-right">
                    <a href="index.php" class="btn btn-outline-light">
                        <i class="fas fa-home"></i> Pagina Principală
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="search-container">
            <h2><i class="fas fa-search"></i> Căutare Ședințe</h2>
            <form method="POST" id="sessionSearchForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="dataSedinta"><i class="fas fa-calendar"></i> Data Ședinței</label>
                            <input type="text" class="form-control" id="dataSedinta" name="dataSedinta" 
                                   placeholder="ZZ.LL.AAAA (ex: 15.03.2023)" 
                                   value="<?php echo isset($_POST['dataSedinta']) ? htmlspecialchars($_POST['dataSedinta']) : ''; ?>">
                            <small class="form-text text-muted">Introduceți data în format românesc</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="institutie"><i class="fas fa-building"></i> Instituția Judecătorească</label>
                            <select class="form-control" id="institutie" name="institutie">
                                <option value="">Selectați o instituție (opțional)</option>
                                <?php foreach ($institutii as $code => $name): ?>
                                    <option value="<?php echo htmlspecialchars($code); ?>" 
                                            <?php echo (isset($_POST['institutie']) && $_POST['institutie'] === $code) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Caută Ședințe
                        </button>
                        <button type="reset" class="btn btn-secondary ml-2">
                            <i class="fas fa-undo"></i> Resetează
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if (!empty($results)): ?>
            <div class="results-container">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3><i class="fas fa-list"></i> Rezultate (<?php echo $totalResults; ?> ședințe)</h3>
                    <div class="export-buttons">
                        <a href="?export=txt&session_results=1&dataSedinta=<?php echo urlencode($_POST['dataSedinta']); ?>&institutie=<?php echo urlencode($_POST['institutie']); ?>" 
                           class="btn btn-success export-btn">
                            <i class="fas fa-download"></i> Export TXT
                        </a>
                        <a href="?export=xlsx&session_results=1&dataSedinta=<?php echo urlencode($_POST['dataSedinta']); ?>&institutie=<?php echo urlencode($_POST['institutie']); ?>" 
                           class="btn btn-success export-btn">
                            <i class="fas fa-file-excel"></i> Export Excel
                        </a>
                        <a href="?export=pdf&session_results=1&dataSedinta=<?php echo urlencode($_POST['dataSedinta']); ?>&institutie=<?php echo urlencode($_POST['institutie']); ?>" 
                           class="btn btn-danger export-btn">
                            <i class="fas fa-file-pdf"></i> Export PDF
                        </a>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th>Instituția</th>
                                <th>Data Ședinței</th>
                                <th>Ora</th>
                                <th>Sala</th>
                                <th>Dosare</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($results as $sedinta): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($sedinta['institutie']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($sedinta['dataSedinta']); ?></td>
                                    <td><?php echo htmlspecialchars($sedinta['oraSedinta']); ?></td>
                                    <td><?php echo htmlspecialchars($sedinta['salaSedinta']); ?></td>
                                    <td>
                                        <?php if (!empty($sedinta['dosare'])): ?>
                                            <?php foreach ($sedinta['dosare'] as $dosar): ?>
                                                <a href="detalii_dosar.php?numar=<?php echo urlencode($dosar['numarDosar']); ?>&institutie=<?php echo urlencode($dosar['institutie']); ?>" 
                                                   class="case-link" 
                                                   data-case-number="<?php echo htmlspecialchars($dosar['numarDosar']); ?>"
                                                   data-institution="<?php echo htmlspecialchars($dosar['institutie']); ?>">
                                                    <?php echo htmlspecialchars($dosar['numarDosar']); ?>
                                                </a><br>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <span class="text-muted">Nu sunt dosare programate</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php elseif ($hasSearchCriteria && empty($error)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Nu au fost găsite ședințe pentru criteriile de căutare specificate.
            </div>
        <?php endif; ?>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inițializăm loading overlay-ul pentru căutare ședințe
        initSessionLoadingOverlay();

        // Inițializăm navigația mobilă
        initMobileNavigation();

        // Inițializăm validarea formularului
        initFormValidation();

        // Inițializăm calendar picker pentru data ședinței
        initDatePicker();

        // Inițializăm dropdown-ul searchable pentru instituții
        initSearchableInstitutionDropdown();

        // Inițializăm gestionarea link-urilor pentru dosare
        initCaseLinksHandling();

        // Afișăm conținutul principal după încărcare
        setTimeout(function() {
            const mainContent = document.getElementById('sessionMainContent');
            if (mainContent) {
                mainContent.classList.add('loaded');
            }
        }, 100);
    });

    /**
     * Inițializează loading overlay-ul pentru căutare ședințe
     */
    function initSessionLoadingOverlay() {
        const form = document.getElementById('sessionSearchForm');
        const overlay = document.getElementById('sessionLoadingOverlay');

        if (form && overlay) {
            form.addEventListener('submit', function() {
                overlay.style.display = 'flex';

                // Ascundem overlay-ul după maximum 30 secunde
                setTimeout(function() {
                    overlay.classList.add('fade-out');
                    setTimeout(function() {
                        overlay.style.display = 'none';
                        overlay.classList.remove('fade-out');
                    }, 500);
                }, 30000);
            });
        }
    }

    /**
     * Inițializează navigația mobilă
     */
    function initMobileNavigation() {
        const mobileToggle = document.getElementById('mobileMenuToggle');
        const mainNav = document.getElementById('mainNavigation');

        if (mobileToggle && mainNav) {
            mobileToggle.addEventListener('click', function() {
                const isOpen = mainNav.classList.contains('mobile-open');

                if (isOpen) {
                    mainNav.classList.remove('mobile-open');
                    mobileToggle.setAttribute('aria-expanded', 'false');
                    mobileToggle.innerHTML = '<i class="fas fa-bars" aria-hidden="true"></i>';
                } else {
                    mainNav.classList.add('mobile-open');
                    mobileToggle.setAttribute('aria-expanded', 'true');
                    mobileToggle.innerHTML = '<i class="fas fa-times" aria-hidden="true"></i>';
                }
            });

            // Închide meniul mobil când se face click pe un link
            const navLinks = mainNav.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    mainNav.classList.remove('mobile-open');
                    mobileToggle.setAttribute('aria-expanded', 'false');
                    mobileToggle.innerHTML = '<i class="fas fa-bars" aria-hidden="true"></i>';
                });
            });
        }
    }

    /**
     * Inițializează validarea formularului
     */
    function initFormValidation() {
        const form = document.getElementById('sessionSearchForm');
        const dateInput = document.getElementById('dataSedinta');

        if (form && dateInput) {
            // Validare în timp real pentru data ședinței
            dateInput.addEventListener('input', function() {
                validateDateInput(this);
            });

            // Validare la submit
            form.addEventListener('submit', function(e) {
                if (!validateForm()) {
                    e.preventDefault();
                    showNotification('Vă rugăm să corectați erorile din formular.', 'danger');
                }
            });
        }
    }

    /**
     * Validează input-ul pentru dată
     */
    function validateDateInput(input) {
        const value = input.value.trim();
        const datePattern = /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/;

        // Eliminăm clasele de validare anterioare
        input.classList.remove('is-valid', 'is-invalid');

        if (value === '') {
            return; // Câmpul este obligatoriu, dar nu validăm dacă este gol
        }

        if (!datePattern.test(value)) {
            input.classList.add('is-invalid');
            return false;
        }

        const matches = value.match(datePattern);
        const day = parseInt(matches[1]);
        const month = parseInt(matches[2]);
        const year = parseInt(matches[3]);

        // Verificăm validitatea datei
        const date = new Date(year, month - 1, day);
        if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
            input.classList.add('is-invalid');
            return false;
        }

        // Verificăm limitele anului
        const currentYear = new Date().getFullYear();
        if (year < 1990 || year > currentYear + 5) {
            input.classList.add('is-invalid');
            return false;
        }

        input.classList.add('is-valid');
        return true;
    }

    /**
     * Validează întregul formular
     */
    function validateForm() {
        const dateInput = document.getElementById('dataSedinta');
        let isValid = true;

        if (dateInput) {
            if (!validateDateInput(dateInput)) {
                isValid = false;
            }

            if (dateInput.value.trim() === '') {
                dateInput.classList.add('is-invalid');
                isValid = false;
            }
        }

        return isValid;
    }

    /**
     * Inițializează date picker pentru data ședinței
     */
    function initDatePicker() {
        const todayBtn = document.getElementById('todayBtn');
        const tomorrowBtn = document.getElementById('tomorrowBtn');
        const dateInput = document.getElementById('dataSedinta');

        if (todayBtn && dateInput) {
            todayBtn.addEventListener('click', function() {
                dateInput.value = formatDateToRomanian(new Date());
                validateDateInput(dateInput);
            });
        }

        if (tomorrowBtn && dateInput) {
            tomorrowBtn.addEventListener('click', function() {
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                dateInput.value = formatDateToRomanian(tomorrow);
                validateDateInput(dateInput);
            });
        }
    }

    /**
     * Inițializează dropdown-ul searchable pentru instituții
     */
    function initSearchableInstitutionDropdown() {
        const searchInput = document.getElementById('institutieSearch');
        const hiddenSelect = document.getElementById('institutie');
        const dropdown = document.getElementById('institutieDropdown');

        if (!searchInput || !hiddenSelect || !dropdown) {
            return;
        }

        // Construim lista de opțiuni din select-ul ascuns
        const options = Array.from(hiddenSelect.options).map(option => ({
            value: option.value,
            text: option.text,
            selected: option.selected
        }));

        // Setăm valoarea inițială dacă există o selecție
        const selectedOption = options.find(opt => opt.selected);
        if (selectedOption && selectedOption.value !== '') {
            searchInput.value = selectedOption.text;
        }

        let highlightedIndex = -1;

        // Funcție pentru afișarea opțiunilor
        function showOptions(filteredOptions = options) {
            dropdown.innerHTML = '';

            if (filteredOptions.length === 0) {
                const noResults = document.createElement('div');
                noResults.className = 'dropdown-item';
                noResults.textContent = 'Nu au fost găsite rezultate';
                noResults.style.fontStyle = 'italic';
                noResults.style.color = '#6c757d';
                dropdown.appendChild(noResults);
            } else {
                filteredOptions.forEach((option, index) => {
                    const item = document.createElement('div');
                    item.className = 'dropdown-item';
                    item.textContent = option.text;
                    item.dataset.value = option.value;
                    item.dataset.index = index;

                    if (option.selected) {
                        item.classList.add('selected');
                    }

                    item.addEventListener('click', function() {
                        selectOption(option);
                    });

                    dropdown.appendChild(item);
                });
            }

            dropdown.classList.add('show');
            highlightedIndex = -1;
        }

        // Funcție pentru selectarea unei opțiuni
        function selectOption(option) {
            searchInput.value = option.text;
            hiddenSelect.value = option.value;
            dropdown.classList.remove('show');

            // Actualizăm starea selected în opțiuni
            options.forEach(opt => opt.selected = false);
            option.selected = true;

            // Trigger change event pentru validare
            hiddenSelect.dispatchEvent(new Event('change'));
        }

        // Funcție pentru filtrarea opțiunilor
        function filterOptions(query) {
            const normalizedQuery = normalizeRomanianText(query.toLowerCase());
            return options.filter(option => {
                const normalizedText = normalizeRomanianText(option.text.toLowerCase());
                return normalizedText.includes(normalizedQuery);
            });
        }

        // Funcție pentru normalizarea textului românesc
        function normalizeRomanianText(text) {
            return text
                .replace(/ă/g, 'a')
                .replace(/â/g, 'a')
                .replace(/î/g, 'i')
                .replace(/ș/g, 's')
                .replace(/ț/g, 't');
        }

        // Event listeners pentru input
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();

            if (query === '') {
                showOptions();
            } else {
                const filtered = filterOptions(query);
                showOptions(filtered);
            }
        });

        searchInput.addEventListener('focus', function() {
            const query = this.value.trim();
            if (query === '') {
                showOptions();
            } else {
                const filtered = filterOptions(query);
                showOptions(filtered);
            }
        });

        // Navigare cu tastatura
        searchInput.addEventListener('keydown', function(e) {
            const items = dropdown.querySelectorAll('.dropdown-item:not([style*="italic"])');

            if (e.key === 'ArrowDown') {
                e.preventDefault();
                highlightedIndex = Math.min(highlightedIndex + 1, items.length - 1);
                updateHighlight(items);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                highlightedIndex = Math.max(highlightedIndex - 1, -1);
                updateHighlight(items);
            } else if (e.key === 'Enter') {
                e.preventDefault();
                if (highlightedIndex >= 0 && items[highlightedIndex]) {
                    const value = items[highlightedIndex].dataset.value;
                    const option = options.find(opt => opt.value === value);
                    if (option) {
                        selectOption(option);
                    }
                }
            } else if (e.key === 'Escape') {
                dropdown.classList.remove('show');
                highlightedIndex = -1;
            }
        });

        // Funcție pentru actualizarea highlight-ului
        function updateHighlight(items) {
            items.forEach((item, index) => {
                item.classList.toggle('highlighted', index === highlightedIndex);
            });

            // Scroll la elementul highlighted
            if (highlightedIndex >= 0 && items[highlightedIndex]) {
                items[highlightedIndex].scrollIntoView({
                    block: 'nearest',
                    behavior: 'smooth'
                });
            }
        }

        // Închide dropdown-ul când se face click în afara lui
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
                dropdown.classList.remove('show');
            }
        });

        // Reset la schimbarea formularului
        const form = document.getElementById('sessionSearchForm');
        if (form) {
            form.addEventListener('reset', function() {
                setTimeout(() => {
                    searchInput.value = '';
                    hiddenSelect.value = '';
                    options.forEach(opt => opt.selected = opt.value === '');
                    dropdown.classList.remove('show');
                }, 10);
            });
        }
    }

    /**
     * Formatează o dată în format românesc DD.MM.YYYY
     */
    function formatDateToRomanian(date) {
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${day}.${month}.${year}`;
    }

    /**
     * Resetează formularul
     */
    function resetForm() {
        const form = document.getElementById('sessionSearchForm');
        if (form) {
            form.reset();

            // Eliminăm clasele de validare
            const inputs = form.querySelectorAll('.form-control');
            inputs.forEach(input => {
                input.classList.remove('is-valid', 'is-invalid');
            });
        }
    }

    /**
     * Inițializează gestionarea link-urilor pentru dosare
     */
    function initCaseLinksHandling() {
        const caseLinks = document.querySelectorAll('.case-link, .case-link-enhanced');

        caseLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                // Adăugăm un indicator vizual că link-ul a fost accesat
                this.style.opacity = '0.8';

                // Verificăm dacă avem parametrii necesari
                const caseNumber = this.dataset.caseNumber;
                const institution = this.dataset.institution;

                if (!caseNumber) {
                    e.preventDefault();
                    showNotification('Eroare: Numărul dosarului nu este disponibil.', 'danger');
                    this.style.opacity = '1';
                    return;
                }

                // Afișăm un mesaj de încărcare
                showNotification(`Se încarcă detaliile dosarului ${caseNumber}...`, 'info');

                // Logăm navigarea pentru debugging
                console.log('Navigare către dosar:', {
                    numar: caseNumber,
                    institutie: institution,
                    url: this.href
                });
            });

            // Restaurăm opacitatea la hover out
            link.addEventListener('mouseleave', function() {
                this.style.opacity = '1';
            });

            // Adăugăm efect de focus pentru accesibilitate
            link.addEventListener('focus', function() {
                this.style.outline = '2px solid #007bff';
                this.style.outlineOffset = '2px';
            });

            link.addEventListener('blur', function() {
                this.style.outline = 'none';
            });
        });
    }

    /**
     * Afișează notificări
     */
    function showNotification(message, type = 'info') {
        // Implementare simplă de notificare
        const alertClass = type === 'danger' ? 'alert-danger' :
                          type === 'success' ? 'alert-success' :
                          type === 'warning' ? 'alert-warning' : 'alert-info';

        const notification = document.createElement('div');
        notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        `;

        document.body.appendChild(notification);

        // Auto-remove după 5 secunde
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    </script>
</body>
</html>
