/**
 * Portal Dosare <PERSON> - JavaScript Principal
 * Versiunea 1.0.0
 */

(function() {
    'use strict';

    // Configurare globală
    const CONFIG = {
        apiBaseUrl: '/api',
        searchDelay: 500,
        maxResults: 100,
        notificationTimeout: 5000
    };

    // Clasa principală a aplicației
    class PortalApp {
        constructor() {
            this.init();
        }

        init() {
            this.setupEventListeners();
            this.setupFormValidation();
            this.setupAutoComplete();
            this.setupNotifications();
            this.setupLoadingStates();
            this.setupResponsiveFeatures();
        }

        setupEventListeners() {
            // Form submission
            const searchForm = document.getElementById('searchForm');
            if (searchForm) {
                searchForm.addEventListener('submit', this.handleSearchSubmit.bind(this));
            }

            // Quick search buttons
            document.querySelectorAll('[data-quick-search]').forEach(button => {
                button.addEventListener('click', this.handleQuickSearch.bind(this));
            });

            // Export buttons
            document.querySelectorAll('[data-export]').forEach(button => {
                button.addEventListener('click', this.handleExport.bind(this));
            });

            // Copy buttons
            document.querySelectorAll('[data-copy]').forEach(button => {
                button.addEventListener('click', this.handleCopy.bind(this));
            });

            // Modal triggers
            document.querySelectorAll('[data-modal]').forEach(trigger => {
                trigger.addEventListener('click', this.handleModal.bind(this));
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
        }

        setupFormValidation() {
            const forms = document.querySelectorAll('form[data-validate]');
            forms.forEach(form => {
                form.addEventListener('submit', this.validateForm.bind(this));
            });
        }

        setupAutoComplete() {
            const searchInput = document.getElementById('bulkSearchTerms');
            if (searchInput) {
                let timeout;
                searchInput.addEventListener('input', (e) => {
                    clearTimeout(timeout);
                    timeout = setTimeout(() => {
                        this.suggestSearchTerms(e.target.value);
                    }, CONFIG.searchDelay);
                });
            }
        }

        setupNotifications() {
            // Create notification container if it doesn't exist
            if (!document.getElementById('notificationContainer')) {
                const container = document.createElement('div');
                container.id = 'notificationContainer';
                container.className = 'position-fixed';
                container.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
                document.body.appendChild(container);
            }
        }

        setupLoadingStates() {
            // Add loading states to buttons
            document.querySelectorAll('button[type="submit"]').forEach(button => {
                button.addEventListener('click', () => {
                    this.setLoadingState(button, true);
                });
            });
        }

        setupResponsiveFeatures() {
            // Handle mobile menu
            const navbarToggler = document.querySelector('.navbar-toggler');
            const navbarCollapse = document.querySelector('.navbar-collapse');
            
            if (navbarToggler && navbarCollapse) {
                navbarToggler.addEventListener('click', () => {
                    navbarCollapse.classList.toggle('show');
                });
            }

            // Handle responsive tables
            this.setupResponsiveTables();
        }

        // Event handlers
        handleSearchSubmit(event) {
            event.preventDefault();
            const form = event.target;
            const formData = new FormData(form);
            
            this.performSearch(formData);
        }

        handleQuickSearch(event) {
            const type = event.target.dataset.quickSearch;
            const query = event.target.dataset.query;
            
            if (query) {
                this.performQuickSearch(type, query);
            }
        }

        handleExport(event) {
            const format = event.target.dataset.export;
            const url = `/search/export/${format}`;
            
            window.location.href = url;
        }

        handleCopy(event) {
            const text = event.target.dataset.copy;
            this.copyToClipboard(text);
        }

        handleModal(event) {
            const modalId = event.target.dataset.modal;
            const modal = new bootstrap.Modal(document.getElementById(modalId));
            modal.show();
        }

        handleKeyboardShortcuts(event) {
            // Ctrl/Cmd + K pentru căutare rapidă
            if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
                event.preventDefault();
                const searchInput = document.getElementById('bulkSearchTerms');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // Escape pentru închiderea modale
            if (event.key === 'Escape') {
                const modals = document.querySelectorAll('.modal.show');
                modals.forEach(modal => {
                    const bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) {
                        bsModal.hide();
                    }
                });
            }
        }

        // API methods
        async performSearch(formData) {
            try {
                this.showLoading();
                
                const response = await fetch('/search/bulk', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                
                if (data.success) {
                    this.displayResults(data.results);
                } else {
                    this.showNotification(data.error, 'error');
                }
            } catch (error) {
                this.showNotification('Eroare la căutare: ' + error.message, 'error');
            } finally {
                this.hideLoading();
            }
        }

        async performQuickSearch(type, query) {
            try {
                const params = new URLSearchParams({
                    q: query,
                    type: type
                });

                const response = await fetch(`${CONFIG.apiBaseUrl}/search?${params}`);
                const data = await response.json();

                if (data.success) {
                    this.displayResults(data.data);
                } else {
                    this.showNotification(data.error, 'error');
                }
            } catch (error) {
                this.showNotification('Eroare la căutare rapidă: ' + error.message, 'error');
            }
        }

        async suggestSearchTerms(query) {
            if (query.length < 3) return;

            try {
                const response = await fetch(`${CONFIG.apiBaseUrl}/suggest?q=${encodeURIComponent(query)}`);
                const data = await response.json();

                if (data.success) {
                    this.displaySuggestions(data.suggestions);
                }
            } catch (error) {
                // Silently fail for suggestions
                console.warn('Could not load suggestions:', error);
            }
        }

        // UI methods
        displayResults(results) {
            const container = document.getElementById('resultsContainer');
            if (!container) return;

            if (results.length === 0) {
                container.innerHTML = this.getNoResultsTemplate();
                return;
            }

            const html = results.map(result => this.getResultTemplate(result)).join('');
            container.innerHTML = html;

            // Add animations
            container.querySelectorAll('.result-item').forEach((item, index) => {
                item.style.animationDelay = `${index * 0.1}s`;
                item.classList.add('fade-in');
            });
        }

        displaySuggestions(suggestions) {
            const container = document.getElementById('suggestionsContainer');
            if (!container) return;

            if (suggestions.length === 0) {
                container.style.display = 'none';
                return;
            }

            const html = suggestions.map(suggestion => 
                `<div class="suggestion-item" onclick="app.selectSuggestion('${suggestion}')">${suggestion}</div>`
            ).join('');

            container.innerHTML = html;
            container.style.display = 'block';
        }

        showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            if (!container) return;

            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                <i class="fas fa-${this.getNotificationIcon(type)} me-2"></i>${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            container.appendChild(alert);

            // Auto-remove after timeout
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, CONFIG.notificationTimeout);
        }

        showLoading() {
            document.body.classList.add('loading');
            this.showNotification('Se procesează căutarea...', 'info');
        }

        hideLoading() {
            document.body.classList.remove('loading');
        }

        setLoadingState(button, loading) {
            if (loading) {
                button.disabled = true;
                button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Se procesează...';
            } else {
                button.disabled = false;
                button.innerHTML = button.dataset.originalText || 'Submit';
            }
        }

        // Utility methods
        async copyToClipboard(text) {
            try {
                await navigator.clipboard.writeText(text);
                this.showNotification('Copiat în clipboard!', 'success');
            } catch (error) {
                // Fallback pentru browsere mai vechi
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                this.showNotification('Copiat în clipboard!', 'success');
            }
        }

        validateForm(event) {
            const form = event.target;
            const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
            let isValid = true;

            inputs.forEach(input => {
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                event.preventDefault();
                this.showNotification('Vă rugăm să completați toate câmpurile obligatorii.', 'warning');
            }

            return isValid;
        }

        setupResponsiveTables() {
            const tables = document.querySelectorAll('.table-responsive');
            tables.forEach(table => {
                const wrapper = document.createElement('div');
                wrapper.className = 'table-responsive';
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            });
        }

        // Template methods
        getResultTemplate(result) {
            return `
                <div class="result-item card mb-3">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>${result.number}</strong>
                                <br><small class="text-muted">${result.institution}</small>
                            </div>
                            <div class="col-md-3">
                                <small>${result.date}</small>
                            </div>
                            <div class="col-md-4">
                                <div class="text-truncate" title="${result.object}">${result.object}</div>
                            </div>
                            <div class="col-md-2">
                                <div class="btn-group btn-group-sm">
                                    <a href="/case/${result.number}/${result.institution}" 
                                       class="btn btn-outline-primary" title="Vezi detalii">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="/case/${result.number}/${result.institution}/pdf" 
                                       class="btn btn-outline-danger" title="Descarcă PDF">
                                        <i class="fas fa-file-pdf"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        getNoResultsTemplate() {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-search fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">Nu au fost găsite rezultate</h4>
                    <p class="text-muted">
                        Încercați să modificați criteriile de căutare sau să folosiți termeni mai generali.
                    </p>
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-2"></i>Înapoi la Căutare
                    </a>
                </div>
            `;
        }

        getNotificationIcon(type) {
            const icons = {
                success: 'check-circle',
                error: 'exclamation-triangle',
                warning: 'exclamation-circle',
                info: 'info-circle'
            };
            return icons[type] || 'info-circle';
        }

        // Public methods
        selectSuggestion(suggestion) {
            const searchInput = document.getElementById('bulkSearchTerms');
            if (searchInput) {
                searchInput.value = suggestion;
                document.getElementById('suggestionsContainer').style.display = 'none';
            }
        }
    }

    // Initialize app when DOM is ready
    document.addEventListener('DOMContentLoaded', () => {
        window.app = new PortalApp();
    });

    // Export for global access
    window.PortalApp = PortalApp;

})();
