<?php
/**
 * Fișier de configurare pentru compatibilitate cu paginile vechi
 * Redirecționează către noua configurație unificată
 */

// Încărcăm bootstrap-ul pentru noua arhitectură
require_once dirname(__DIR__) . '/bootstrap.php';

// Obținem configurația unificată
$appConfig = \App\Config\AppConfig::getInstance();

// Definim constantele pentru compatibilitate cu codul vechi
if (!defined('APP_NAME')) {
    define('APP_NAME', $appConfig->get('app.name'));
}

if (!defined('SOAP_WSDL')) {
    define('SOAP_WSDL', $appConfig->get('soap.wsdl'));
}

if (!defined('SOAP_ENDPOINT')) {
    define('SOAP_ENDPOINT', $appConfig->get('soap.endpoint'));
}

if (!defined('SOAP_NAMESPACE')) {
    define('SOAP_NAMESPACE', $appConfig->get('soap.namespace'));
}

if (!defined('RESULTS_PER_PAGE')) {
    define('RESULTS_PER_PAGE', $appConfig->get('app.results_per_page', 25));
}

if (!defined('DATE_FORMAT')) {
    define('DATE_FORMAT', $appConfig->get('app.date_format', 'd.m.Y'));
}

if (!defined('DATETIME_FORMAT')) {
    define('DATETIME_FORMAT', $appConfig->get('app.datetime_format', 'd.m.Y H:i'));
}

if (!defined('DEBUG_MODE')) {
    define('DEBUG_MODE', $appConfig->get('app.debug', false));
}

// Funcție pentru afișarea erorilor în modul debug
if (!function_exists('debug')) {
    function debug($data) {
        if (DEBUG_MODE) {
            echo '<pre>';
            print_r($data);
            echo '</pre>';
        }
    }
}

// Configurăm TCPDF pentru compatibilitate
if (!defined('K_TCPDF_EXTERNAL_CONFIG')) {
    define('K_TCPDF_EXTERNAL_CONFIG', true);
}

// Variabile globale pentru compatibilitate
$config = $appConfig;
?>
