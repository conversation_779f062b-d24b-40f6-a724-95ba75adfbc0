RewriteEngine On

# Redirecționează toate cererile către public/index.php pentru rutele noi
# Dar permite accesul direct la fișierele legacy existente

# Permite accesul direct la fișierele și directoarele existente
RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^(.*)$ - [L]

# Redirecționează rutele specifice către paginile legacy funcționale
RewriteRule ^search/bulk/?$ avans.php [QSA,L]
RewriteRule ^search/?$ avans.php [QSA,L]

# Pentru alte rute care nu sunt fișiere existente, redirecționează către public/
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/public/
RewriteCond %{REQUEST_URI} !^/(avans|avansat|sedinte|detalii_dosar|search|index)\.php
RewriteRule ^(.*)$ public/index.php [QSA,L]

# Setări de securitate
<Files "*.php">
    Require all granted
</Files>

# Protejează fișierele sensibile
<Files ".env">
    Require all denied
</Files>

<Files "composer.json">
    Require all denied
</Files>

<Files "composer.lock">
    Require all denied
</Files>

<Files "bootstrap.php">
    Require all denied
</Files>

<Files "routes.php">
    Require all denied
</Files>

# Protejează directoarele sensibile prin RewriteRule
RewriteRule ^\.git/ - [F,L]
RewriteRule ^\.svn/ - [F,L]
RewriteRule ^vendor/ - [F,L]
RewriteRule ^src/ - [F,L]
RewriteRule ^config/ - [F,L]

# Setări pentru cache
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# Compresie GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Setări pentru securitate
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>
