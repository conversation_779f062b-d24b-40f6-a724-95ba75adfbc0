<?php

namespace App\Services;

use App\Config\AppConfig;
use TCPDF;
use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception as PHPMailerException;

/**
 * Serviciu pentru generarea PDF-urilor și trimiterea prin email
 */
class PdfService
{
    private AppConfig $config;

    public function __construct()
    {
        $this->config = AppConfig::getInstance();
    }

    /**
     * Generează PDF pentru un dosar
     */
    public function generateCasePdf(object $dosar, ?string $filename = null, bool $inline = false): void
    {
        $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

        // Setări document
        $pdf->SetCreator('Portal Dosare Judecătorești');
        $pdf->SetAuthor('Portal Dosare Judecătorești');
        $pdf->SetTitle('Dosar ' . ($dosar->numar ?? 'N/A'));
        $pdf->SetSubject('Detalii Dosar Judecătoresc');

        // Setări header și footer
        $pdf->setHeaderData('', 0, 'Portal Dosare Judecătorești', 'Detalii Dosar');
        $pdf->setFooterData([0, 0, 0], [0, 0, 0]);
        $pdf->setHeaderFont([PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN]);
        $pdf->setFooterFont([PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA]);

        // Setări margini
        $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
        $pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
        $pdf->SetFooterMargin(PDF_MARGIN_FOOTER);

        // Setări auto page break
        $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);

        // Setări image scale factor
        $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

        // Adăugăm o pagină
        $pdf->AddPage();

        // Conținutul PDF-ului
        $html = $this->generateCaseHtml($dosar);
        $pdf->writeHTML($html, true, false, true, false, '');

        // Output
        if ($inline) {
            $pdf->Output('', 'I');
        } else {
            $pdf->Output($filename ?? 'dosar.pdf', 'D');
        }
    }

    /**
     * Generează PDF pentru ședințe
     */
    public function generateSessionsPdf(array $sessions, string $filename, string $institutie): void
    {
        $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

        // Setări document
        $pdf->SetCreator('Portal Dosare Judecătorești');
        $pdf->SetAuthor('Portal Dosare Judecătorești');
        $pdf->SetTitle('Ședințe - ' . $institutie);
        $pdf->SetSubject('Ședințe Judecătorești');

        // Setări header și footer
        $pdf->setHeaderData('', 0, 'Portal Dosare Judecătorești', 'Ședințe Judecătorești');
        $pdf->setFooterData([0, 0, 0], [0, 0, 0]);
        $pdf->setHeaderFont([PDF_FONT_NAME_MAIN, '', PDF_FONT_SIZE_MAIN]);
        $pdf->setFooterFont([PDF_FONT_NAME_DATA, '', PDF_FONT_SIZE_DATA]);

        // Setări margini
        $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
        $pdf->SetHeaderMargin(PDF_MARGIN_HEADER);
        $pdf->SetFooterMargin(PDF_MARGIN_FOOTER);

        // Setări auto page break
        $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);

        // Setări image scale factor
        $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

        // Adăugăm o pagină
        $pdf->AddPage();

        // Conținutul PDF-ului
        $html = $this->generateSessionsHtml($sessions, $institutie);
        $pdf->writeHTML($html, true, false, true, false, '');

        // Output
        $pdf->Output($filename, 'D');
    }

    /**
     * Trimite dosarul prin email
     */
    public function sendCaseByEmail(object $dosar, string $email): void
    {
        try {
            $mail = new PHPMailer(true);

            // Configurare server
            $mail->isSMTP();
            $mail->Host = $this->config->get('mail.host');
            $mail->SMTPAuth = true;
            $mail->Username = $this->config->get('mail.username');
            $mail->Password = $this->config->get('mail.password');
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = $this->config->get('mail.port');
            $mail->CharSet = 'UTF-8';

            // Destinatar
            $mail->setFrom($this->config->get('mail.from_address'), $this->config->get('mail.from_name'));
            $mail->addAddress($email);

            // Conținut
            $mail->isHTML(true);
            $mail->Subject = 'Detalii Dosar ' . ($dosar->numar ?? 'N/A');
            $mail->Body = $this->generateEmailHtml($dosar);

            // Atașăm PDF-ul
            $pdfContent = $this->generatePdfContent($dosar);
            $mail->addStringAttachment($pdfContent, 'dosar.pdf', 'base64', 'application/pdf');

            $mail->send();
        } catch (PHPMailerException $e) {
            throw new \Exception('Eroare la trimiterea email-ului: ' . $e->getMessage());
        }
    }

    /**
     * Generează conținutul HTML pentru dosar
     */
    private function generateCaseHtml(object $dosar): string
    {
        $html = '
        <style>
            body { font-family: Arial, sans-serif; font-size: 12px; }
            .header { text-align: center; font-size: 18px; font-weight: bold; margin-bottom: 20px; }
            .section { margin-bottom: 15px; }
            .section-title { font-weight: bold; font-size: 14px; margin-bottom: 5px; }
            .field { margin-bottom: 5px; }
            .field-label { font-weight: bold; }
            .parties-table { width: 100%; border-collapse: collapse; margin-top: 10px; }
            .parties-table th, .parties-table td { border: 1px solid #ddd; padding: 5px; }
            .parties-table th { background-color: #f5f5f5; }
        </style>
        
        <div class="header">DETALII DOSAR JUDECĂTORESC</div>
        
        <div class="section">
            <div class="section-title">Informații Generale</div>
            <div class="field">
                <span class="field-label">Număr dosar:</span> ' . htmlspecialchars($dosar->numar ?? 'N/A') . '
            </div>
            <div class="field">
                <span class="field-label">Instituție:</span> ' . htmlspecialchars($dosar->institutie ?? 'N/A') . '
            </div>
            <div class="field">
                <span class="field-label">Data:</span> ' . htmlspecialchars($dosar->data ?? 'N/A') . '
            </div>
            <div class="field">
                <span class="field-label">Obiect:</span> ' . htmlspecialchars($dosar->obiect ?? 'N/A') . '
            </div>
            <div class="field">
                <span class="field-label">Stadiu procesual:</span> ' . htmlspecialchars($dosar->stadiuProcesual ?? 'N/A') . '
            </div>
            <div class="field">
                <span class="field-label">Categorie caz:</span> ' . htmlspecialchars($dosar->categorieCaz ?? 'N/A') . '
            </div>
            <div class="field">
                <span class="field-label">Data ultimei modificări:</span> ' . htmlspecialchars($dosar->dataModificare ?? 'N/A') . '
            </div>
        </div>';

        // Părțile
        if (!empty($dosar->parti)) {
            $html .= '
            <div class="section">
                <div class="section-title">Părțile</div>
                <table class="parties-table">
                    <thead>
                        <tr>
                            <th>Nume</th>
                            <th>Calitate</th>
                        </tr>
                    </thead>
                    <tbody>';
            
            foreach ($dosar->parti as $parte) {
                $html .= '
                        <tr>
                            <td>' . htmlspecialchars($parte->nume ?? '') . '</td>
                            <td>' . htmlspecialchars($parte->calitate ?? '') . '</td>
                        </tr>';
            }
            
            $html .= '
                    </tbody>
                </table>
            </div>';
        }

        // Ședințele
        if (!empty($dosar->sedinte)) {
            $html .= '
            <div class="section">
                <div class="section-title">Ședințe</div>';
            
            foreach ($dosar->sedinte as $sedinta) {
                $html .= '
                <div class="field">
                    <span class="field-label">Data:</span> ' . htmlspecialchars($sedinta->data ?? 'N/A') . '
                    <span class="field-label">Ora:</span> ' . htmlspecialchars($sedinta->ora ?? 'N/A') . '
                </div>
                <div class="field">
                    <span class="field-label">Complet:</span> ' . htmlspecialchars($sedinta->complet ?? 'N/A') . '
                </div>
                <div class="field">
                    <span class="field-label">Soluție:</span> ' . htmlspecialchars($sedinta->solutie ?? 'N/A') . '
                </div>
                <hr>';
            }
            
            $html .= '</div>';
        }

        // Căile de atac
        if (!empty($dosar->caiAtac)) {
            $html .= '
            <div class="section">
                <div class="section-title">Căi de Atac</div>';
            
            foreach ($dosar->caiAtac as $caleAtac) {
                $html .= '
                <div class="field">
                    <span class="field-label">Data declarare:</span> ' . htmlspecialchars($caleAtac->dataDeclarare ?? 'N/A') . '
                </div>
                <div class="field">
                    <span class="field-label">Parte declaratoare:</span> ' . htmlspecialchars($caleAtac->parteDeclaratoare ?? 'N/A') . '
                </div>
                <div class="field">
                    <span class="field-label">Tip cale atac:</span> ' . htmlspecialchars($caleAtac->tipCaleAtac ?? 'N/A') . '
                </div>
                <div class="field">
                    <span class="field-label">Număr dosar instanță superioară:</span> ' . htmlspecialchars($caleAtac->numarDosarInstantaSuperior ?? 'N/A') . '
                </div>
                <hr>';
            }
            
            $html .= '</div>';
        }

        return $html;
    }

    /**
     * Generează conținutul HTML pentru ședințe
     */
    private function generateSessionsHtml(array $sessions, string $institutie): string
    {
        $html = '
        <style>
            body { font-family: Arial, sans-serif; font-size: 12px; }
            .header { text-align: center; font-size: 18px; font-weight: bold; margin-bottom: 20px; }
            .section { margin-bottom: 15px; }
            .section-title { font-weight: bold; font-size: 14px; margin-bottom: 5px; }
            .session { margin-bottom: 15px; border: 1px solid #ddd; padding: 10px; }
            .session-title { font-weight: bold; margin-bottom: 5px; }
            .field { margin-bottom: 5px; }
            .field-label { font-weight: bold; }
        </style>
        
        <div class="header">ȘEDINȚE JUDECĂTOREȘTI</div>
        <div class="section">
            <div class="section-title">Instituție: ' . htmlspecialchars($institutie) . '</div>
            <div class="section-title">Data generării: ' . date('d.m.Y H:i') . '</div>
        </div>';

        foreach ($sessions as $index => $session) {
            $html .= '
            <div class="session">
                <div class="session-title">Ședința ' . ($index + 1) . '</div>
                <div class="field">
                    <span class="field-label">Data:</span> ' . htmlspecialchars($session->data ?? 'N/A') . '
                </div>
                <div class="field">
                    <span class="field-label">Ora:</span> ' . htmlspecialchars($session->ora ?? 'N/A') . '
                </div>
                <div class="field">
                    <span class="field-label">Complet:</span> ' . htmlspecialchars($session->complet ?? 'N/A') . '
                </div>
                <div class="field">
                    <span class="field-label">Soluție:</span> ' . htmlspecialchars($session->solutie ?? 'N/A') . '
                </div>
            </div>';
        }

        return $html;
    }

    /**
     * Generează conținutul HTML pentru email
     */
    private function generateEmailHtml(object $dosar): string
    {
        return '
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial, sans-serif; font-size: 14px; line-height: 1.6; }
                .header { background-color: #f8f9fa; padding: 20px; text-align: center; }
                .content { padding: 20px; }
                .field { margin-bottom: 10px; }
                .field-label { font-weight: bold; color: #333; }
                .footer { background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Detalii Dosar Judecătoresc</h1>
            </div>
            <div class="content">
                <p>Bună ziua,</p>
                <p>Vă trimitem în atașament detaliile dosarului solicitat:</p>
                
                <div class="field">
                    <span class="field-label">Număr dosar:</span> ' . htmlspecialchars($dosar->numar ?? 'N/A') . '
                </div>
                <div class="field">
                    <span class="field-label">Instituție:</span> ' . htmlspecialchars($dosar->institutie ?? 'N/A') . '
                </div>
                <div class="field">
                    <span class="field-label">Data:</span> ' . htmlspecialchars($dosar->data ?? 'N/A') . '
                </div>
                <div class="field">
                    <span class="field-label">Obiect:</span> ' . htmlspecialchars($dosar->obiect ?? 'N/A') . '
                </div>
                
                <p>Detaliile complete se află în fișierul PDF atașat.</p>
                
                <p>Cu stimă,<br>
                Portal Dosare Judecătorești</p>
            </div>
            <div class="footer">
                <p>Acest email a fost generat automat de Portalul Dosarelor Judecătorești.</p>
            </div>
        </body>
        </html>';
    }

    /**
     * Generează conținutul PDF ca string
     */
    private function generatePdfContent(object $dosar): string
    {
        $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

        // Setări document
        $pdf->SetCreator('Portal Dosare Judecătorești');
        $pdf->SetAuthor('Portal Dosare Judecătorești');
        $pdf->SetTitle('Dosar ' . ($dosar->numar ?? 'N/A'));

        // Setări margini
        $pdf->SetMargins(PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT);
        $pdf->SetAutoPageBreak(TRUE, PDF_MARGIN_BOTTOM);

        // Adăugăm o pagină
        $pdf->AddPage();

        // Conținutul PDF-ului
        $html = $this->generateCaseHtml($dosar);
        $pdf->writeHTML($html, true, false, true, false, '');

        // Returnăm conținutul ca string
        return $pdf->Output('', 'S');
    }
} 