<?php
/**
 * Script de audit pentru funcționalitatea de căutare
 * Testează căutarea în masă, filtrele avansate și procesarea rezultatelor
 */

// Configurare pentru capturarea erorilor
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Încărcăm configurația
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "AUDIT FUNCȚIONALITATE CĂUTARE\n";
echo "==============================\n";
echo "Data: " . date('Y-m-d H:i:s') . "\n\n";

// Test funcții de căutare
echo "=== TEST FUNCȚII DE CĂUTARE ===\n";

// Verificare funcții disponibile
$functions = [
    'getInstanteList',
    'searchDosare',
    'searchByNumber',
    'searchByParty',
    'searchByObject',
    'validateRomanianDate',
    'convertRomanianDateToSoap',
    'cleanForExcel',
    'showNotification'
];

foreach ($functions as $function) {
    if (function_exists($function)) {
        echo "✅ Funcția $function este disponibilă\n";
    } else {
        echo "❌ Funcția $function nu este disponibilă\n";
    }
}

// Test lista de instanțe
echo "\n=== TEST LISTA INSTANȚE ===\n";
try {
    $instante = getInstanteList();
    if (is_array($instante) && count($instante) > 0) {
        echo "✅ Lista instanțelor încărcată cu succes (" . count($instante) . " instanțe)\n";
        echo "Primele 5 instanțe:\n";
        $count = 0;
        foreach ($instante as $cod => $nume) {
            if ($count >= 5) break;
            echo "  $cod: $nume\n";
            $count++;
        }
    } else {
        echo "❌ Lista instanțelor nu a putut fi încărcată\n";
    }
} catch (Exception $e) {
    echo "❌ Eroare la încărcarea listei instanțelor: " . $e->getMessage() . "\n";
}

// Test validare date românești
echo "\n=== TEST VALIDARE DATE ROMÂNEȘTI ===\n";
$testDates = [
    '01.01.2023' => true,
    '31.12.2023' => true,
    '29.02.2024' => true,  // An bisect
    '29.02.2023' => false, // Nu e an bisect
    '32.01.2023' => false, // Zi invalidă
    '01.13.2023' => false, // Lună invalidă
    '1.1.2023' => true,    // Format scurt
    '01/01/2023' => false, // Format greșit
    '' => false,           // Gol
    'invalid' => false     // Text invalid
];

foreach ($testDates as $date => $expected) {
    if (function_exists('validateRomanianDate')) {
        $result = validateRomanianDate($date);
        $status = ($result === $expected) ? "✅" : "❌";
        echo "$status Data '$date': " . ($result ? "validă" : "invalidă") . 
             ($result !== $expected ? " (NEAȘTEPTAT!)" : "") . "\n";
    } else {
        echo "❌ Funcția validateRomanianDate nu este disponibilă\n";
        break;
    }
}

// Test conversie date pentru SOAP
echo "\n=== TEST CONVERSIE DATE SOAP ===\n";
$testConversions = [
    '01.01.2023' => '2023-01-01T00:00:00',
    '31.12.2023' => '2023-12-31T00:00:00',
    '15.06.2023' => '2023-06-15T00:00:00'
];

foreach ($testConversions as $romanianDate => $expectedSoap) {
    if (function_exists('convertRomanianDateToSoap')) {
        $result = convertRomanianDateToSoap($romanianDate);
        $status = ($result === $expectedSoap) ? "✅" : "❌";
        echo "$status '$romanianDate' -> '$result'" . 
             ($result !== $expectedSoap ? " (Așteptat: '$expectedSoap')" : "") . "\n";
    } else {
        echo "❌ Funcția convertRomanianDateToSoap nu este disponibilă\n";
        break;
    }
}

// Test detectare tip căutare
echo "\n=== TEST DETECTARE TIP CĂUTARE ===\n";
$searchTerms = [
    '1234/2023' => 'număr dosar',
    '1234/23' => 'număr dosar',
    'Popescu Ion' => 'nume persoană',
    'divorț' => 'obiect dosar',
    'executare silită' => 'obiect dosar',
    'SC COMPANY SRL' => 'nume persoană',
    '12345' => 'ambiguu'
];

foreach ($searchTerms as $term => $expectedType) {
    // Simulăm logica de detectare
    $detectedType = 'necunoscut';
    if (preg_match('/^\d+\/\d{2,4}$/', $term)) {
        $detectedType = 'număr dosar';
    } elseif (preg_match('/\b(divorț|executare|acțiune|cerere|contestație)\b/i', $term)) {
        $detectedType = 'obiect dosar';
    } elseif (preg_match('/[a-zA-ZăâîșțĂÂÎȘȚ\s]+/', $term)) {
        $detectedType = 'nume persoană';
    }
    
    $status = ($detectedType === $expectedType || $expectedType === 'ambiguu') ? "✅" : "❌";
    echo "$status '$term' -> $detectedType\n";
}

// Test procesare diacritice românești
echo "\n=== TEST PROCESARE DIACRITICE ===\n";
$diacriticsTests = [
    'Popescu' => 'Popescu',
    'Ștefan' => 'Stefan',
    'Țuțu' => 'Tutu',
    'Mănuțiu' => 'Manutiu',
    'Râmnicu' => 'Ramnicu'
];

foreach ($diacriticsTests as $original => $expected) {
    // Simulăm funcția de normalizare diacritice
    $normalized = str_replace(
        ['ă', 'â', 'î', 'ș', 'ț', 'Ă', 'Â', 'Î', 'Ș', 'Ț'],
        ['a', 'a', 'i', 's', 't', 'A', 'A', 'I', 'S', 'T'],
        $original
    );
    
    $status = ($normalized === $expected) ? "✅" : "❌";
    echo "$status '$original' -> '$normalized'\n";
}

// Test simulare căutare în masă
echo "\n=== TEST SIMULARE CĂUTARE ÎN MASĂ ===\n";
$bulkSearchTerms = "1234/2023\nPopescu Ion\ndivorț\n5678/2024";
$terms = array_filter(array_map('trim', explode("\n", $bulkSearchTerms)));

echo "Termeni de căutare: " . count($terms) . "\n";
foreach ($terms as $index => $term) {
    echo "  " . ($index + 1) . ". '$term'\n";
}

// Simulăm progresul căutării
echo "\nSimulare progres căutare:\n";
foreach ($terms as $index => $term) {
    $progress = round((($index + 1) / count($terms)) * 100);
    echo "[$progress%] Căutare pentru: '$term'\n";
    usleep(100000); // Simulăm delay
}

echo "\n=== TEST FILTRE AVANSATE ===\n";

// Test categorii de instanțe
$categoriiInstante = [
    'CurteadeApel' => 'Curte de Apel',
    'Tribunalul' => 'Tribunal',
    'JudecatoriaOras' => 'Judecătoria Oraș',
    'JudecatoriaSector' => 'Judecătoria Sector'
];

echo "Categorii instanțe disponibile:\n";
foreach ($categoriiInstante as $cod => $nume) {
    echo "  $cod: $nume\n";
}

// Test categorii cazuri
$categoriiCazuri = [
    'civil' => 'Civil',
    'penal' => 'Penal',
    'comercial' => 'Comercial',
    'contencios' => 'Contencios Administrativ'
];

echo "\nCategorii cazuri disponibile:\n";
foreach ($categoriiCazuri as $cod => $nume) {
    echo "  $cod: $nume\n";
}

// Test combinații filtre
echo "\n=== TEST COMBINAȚII FILTRE ===\n";
$testFilters = [
    [
        'institutie' => 'TBBU',
        'categorieInstanta' => 'Tribunalul',
        'categorieCaz' => 'civil',
        'dataInceput' => '01.01.2023',
        'dataSfarsit' => '31.12.2023'
    ],
    [
        'institutie' => '',
        'categorieInstanta' => 'CurteadeApel',
        'categorieCaz' => 'penal',
        'dataInceput' => '',
        'dataSfarsit' => ''
    ]
];

foreach ($testFilters as $index => $filters) {
    echo "Combinația " . ($index + 1) . ":\n";
    foreach ($filters as $key => $value) {
        echo "  $key: " . ($value ?: '(gol)') . "\n";
    }
    echo "  Status: " . (count(array_filter($filters)) > 0 ? "✅ Filtre active" : "❌ Fără filtre") . "\n\n";
}

echo "=== SUMAR AUDIT CĂUTARE ===\n";
echo "Testarea funcționalității de căutare completă.\n";
echo "Verificați rezultatele de mai sus pentru identificarea problemelor.\n";
?>
