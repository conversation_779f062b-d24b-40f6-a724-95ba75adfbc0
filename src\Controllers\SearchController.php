<?php

namespace App\Controllers;

use App\Services\DosarService;
use App\Services\PdfService;

/**
 * Controller pentru funcționalitățile de căutare
 */
class SearchController extends BaseController
{
    private DosarService $dosarService;
    private PdfService $pdfService;

    public function __construct()
    {
        parent::__construct();
        $this->dosarService = new DosarService();
        $this->pdfService = new PdfService();
    }

    /**
     * Afișează pagina principală de căutare
     */
    public function index(): void
    {
        $data = [
            'instante' => $this->getInstanteList(),
            'pageTitle' => 'Portal Dosare Judecătorești - Căutare'
        ];

        echo $this->templateEngine->render('search/index.twig', $data);
    }

    /**
     * Afișează pagina de căutare în masă
     */
    public function showBulkSearch(): void
    {
        $data = [
            'instante' => $this->getInstanteList(),
            'pageTitle' => 'Căutare în Masă - Portal Dosare Judecătorești'
        ];

        echo $this->templateEngine->render('search/bulk.twig', $data);
    }

    /**
     * Procesează căutarea în masă
     */
    public function bulkSearch(): void
    {
        $postData = $this->getPostData();
        
        $bulkSearchTerms = $postData['bulkSearchTerms'] ?? '';
        $advancedFilters = [
            'institutie' => $postData['institutie'] ?? '',
            'categorieInstanta' => $postData['categorieInstanta'] ?? '',
            'categorieCaz' => $postData['categorieCaz'] ?? '',
            'dataInceput' => $postData['dataInceput'] ?? '',
            'dataSfarsit' => $postData['dataSfarsit'] ?? ''
        ];

        $results = [];
        $error = null;
        $totalResults = 0;

        if (!empty($bulkSearchTerms) || $this->hasAdvancedFilters($advancedFilters)) {
            try {
                if (!empty($bulkSearchTerms)) {
                    $searchTermsData = $this->parseBulkSearchTerms($bulkSearchTerms);
                    
                    if (count($searchTermsData) > 100) {
                        $error = 'Numărul maxim de termeni de căutare este 100.';
                    } elseif (count($searchTermsData) === 0) {
                        $error = 'Nu au fost găsiți termeni valizi de căutare.';
                    } else {
                        $results = $this->performBulkSearchWithFilters($searchTermsData, $advancedFilters);
                        $totalResults = $this->calculateTotalResults($results);
                    }
                } else {
                    // Căutare doar cu filtre
                    $results = $this->performFilterOnlySearch($advancedFilters);
                    $totalResults = $this->calculateTotalResults($results);
                }
            } catch (\Exception $e) {
                $error = 'Eroare la căutarea în masă: ' . $e->getMessage();
                error_log('Eroare bulk search: ' . $e->getMessage());
            }
        }

        $data = [
            'results' => $results,
            'totalResults' => $totalResults,
            'error' => $error,
            'searchTerms' => $bulkSearchTerms,
            'advancedFilters' => $advancedFilters,
            'instante' => $this->getInstanteList(),
            'pageTitle' => 'Rezultate Căutare'
        ];

        echo $this->templateEngine->render('search/results.twig', $data);
    }

    /**
     * Exportă rezultatele în CSV
     */
    public function exportCsv(): void
    {
        $getData = $this->getGetData();
        $results = $this->getResultsFromSession();
        
        if (empty($results)) {
            $this->json(['error' => 'Nu există rezultate pentru export'], 400);
            return;
        }

        $filename = 'export_dosare_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $this->generateCsvContent($results);
    }

    /**
     * Exportă rezultatele în Excel
     */
    public function exportExcel(): void
    {
        $getData = $this->getGetData();
        $results = $this->getResultsFromSession();
        
        if (empty($results)) {
            $this->json(['error' => 'Nu există rezultate pentru export'], 400);
            return;
        }

        $filename = 'export_dosare_' . date('Y-m-d_H-i-s') . '.xlsx';
        
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $this->generateExcelContent($results);
    }

    /**
     * Căutare simplă după număr dosar
     */
    public function searchByNumber(): void
    {
        $postData = $this->getPostData();
        
        $numarDosar = $postData['numarDosar'] ?? '';
        $institutie = $postData['institutie'] ?? '';
        
        if (empty($numarDosar)) {
            $this->json(['error' => 'Numărul dosarului este obligatoriu'], 400);
            return;
        }

        try {
            $results = $this->dosarService->cautareDupaNumarDosar($numarDosar, $institutie);
            $this->json(['results' => $results, 'count' => count($results)]);
        } catch (\Exception $e) {
            $this->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Căutare după nume parte
     */
    public function searchByParty(): void
    {
        $postData = $this->getPostData();
        
        $numeParte = $postData['numeParte'] ?? '';
        $institutie = $postData['institutie'] ?? '';
        
        if (empty($numeParte)) {
            $this->json(['error' => 'Numele părții este obligatoriu'], 400);
            return;
        }

        try {
            $results = $this->dosarService->cautareDupaNumeParte($numeParte, $institutie);
            $this->json(['results' => $results, 'count' => count($results)]);
        } catch (\Exception $e) {
            $this->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Căutare după obiect
     */
    public function searchByObject(): void
    {
        $postData = $this->getPostData();
        
        $obiectDosar = $postData['obiectDosar'] ?? '';
        $institutie = $postData['institutie'] ?? '';
        
        if (empty($obiectDosar)) {
            $this->json(['error' => 'Obiectul dosarului este obligatoriu'], 400);
            return;
        }

        try {
            $results = $this->dosarService->cautareDupaObiect($obiectDosar, $institutie);
            $this->json(['results' => $results, 'count' => count($results)]);
        } catch (\Exception $e) {
            $this->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Afișează pagina de contact
     */
    public function contact(): void
    {
        $this->render('contact/index.twig', [
            'pageTitle' => 'Contact - Portal Dosare Judecătorești'
        ]);
    }

    /**
     * Afișează documentația API
     */
    public function api(): void
    {
        $this->render('api/documentation.twig', [
            'pageTitle' => 'API Documentation - Portal Dosare Judecătorești',
            'baseUrl' => $_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST']
        ]);
    }

    // Metode helper private

    private function getInstanteList(): array
    {
        return [
            'JudecatoriaSECTORUL1BUCURESTI' => 'Judecătoria Sectorului 1 București',
            'JudecatoriaSECTORUL2BUCURESTI' => 'Judecătoria Sectorului 2 București',
            'JudecatoriaSECTORUL3BUCURESTI' => 'Judecătoria Sectorului 3 București',
            'JudecatoriaSECTORUL4BUCURESTI' => 'Judecătoria Sectorului 4 București',
            'JudecatoriaSECTORUL5BUCURESTI' => 'Judecătoria Sectorului 5 București',
            'JudecatoriaSECTORUL6BUCURESTI' => 'Judecătoria Sectorului 6 București',
            'TribunalulBUCURESTI' => 'Tribunalul București',
            'CurteadeApelBUCURESTI' => 'Curtea de Apel București',
            'InaltaCurtedeCasatiesiJustitie' => 'Înalta Curte de Casație și Justiție'
        ];
    }

    private function hasAdvancedFilters(array $filters): bool
    {
        return !empty($filters['institutie']) ||
               !empty($filters['categorieInstanta']) ||
               !empty($filters['categorieCaz']) ||
               !empty($filters['dataInceput']) ||
               !empty($filters['dataSfarsit']);
    }

    private function parseBulkSearchTerms(string $input): array
    {
        $terms = [];
        $lines = explode("\n", $input);
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (!empty($line)) {
                $terms[] = [
                    'term' => $line,
                    'type' => $this->detectSearchType($line)
                ];
            }
        }
        
        return $terms;
    }

    private function detectSearchType(string $term): string
    {
        $cleanTerm = trim($term, '"\'');
        
        if (preg_match('/^\d+\/\d+(?:\/\d+)?$/', $cleanTerm)) {
            return 'numarDosar';
        }
        
        if (preg_match('/^(?:nr\.?\s*|dosar\s*|număr\s*)?(\d+\/\d+(?:\/\d+)?)$/i', $cleanTerm)) {
            return 'numarDosar';
        }
        
        return 'numeParte';
    }

    private function performBulkSearchWithFilters(array $searchTerms, array $filters): array
    {
        $results = [];
        
        foreach ($searchTerms as $searchTerm) {
            try {
                $termResults = [];
                
                switch ($searchTerm['type']) {
                    case 'numarDosar':
                        $termResults = $this->dosarService->cautareDupaNumarDosar(
                            $searchTerm['term'],
                            $filters['institutie'] ?? ''
                        );
                        break;
                    case 'numeParte':
                        $termResults = $this->dosarService->cautareDupaNumeParte(
                            $searchTerm['term'],
                            $filters['institutie'] ?? ''
                        );
                        break;
                }
                
                $results[] = [
                    'term' => $searchTerm['term'],
                    'type' => $searchTerm['type'],
                    'results' => $termResults,
                    'count' => count($termResults)
                ];
                
            } catch (\Exception $e) {
                $results[] = [
                    'term' => $searchTerm['term'],
                    'type' => $searchTerm['type'],
                    'results' => [],
                    'count' => 0,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }

    private function performFilterOnlySearch(array $filters): array
    {
        // Implementare pentru căutare doar cu filtre
        return [];
    }

    private function calculateTotalResults(array $results): int
    {
        $total = 0;
        foreach ($results as $result) {
            $total += $result['count'] ?? 0;
        }
        return $total;
    }

    private function getResultsFromSession(): array
    {
        return $_SESSION['search_results'] ?? [];
    }

    private function generateCsvContent(array $results): void
    {
        $output = fopen('php://output', 'w');
        
        // BOM pentru UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Header
        fputcsv($output, [
            'Număr Dosar',
            'Instituție',
            'Data',
            'Obiect',
            'Stadiu Procesual',
            'Părți'
        ]);
        
        // Date
        foreach ($results as $result) {
            foreach ($result['results'] as $dosar) {
                fputcsv($output, [
                    $dosar->numar ?? '',
                    $dosar->institutie ?? '',
                    $dosar->data ?? '',
                    $dosar->obiect ?? '',
                    $dosar->stadiuProcesual ?? '',
                    $this->formatParties($dosar->parti ?? [])
                ]);
            }
        }
        
        fclose($output);
    }

    private function generateExcelContent(array $results): void
    {
        // Implementare pentru Excel
        // Aceasta va fi implementată în PdfService
    }

    private function formatParties(array $parties): string
    {
        $formatted = [];
        foreach ($parties as $party) {
            $formatted[] = ($party->nume ?? '') . ' (' . ($party->calitate ?? '') . ')';
        }
        return implode('; ', $formatted);
    }
} 