<?php

use App\Router;
use App\Controllers\SearchController;
use App\Controllers\CaseController;
use App\Controllers\ApiController;

/**
 * Configurarea rutelor pentru aplicație
 */

$router = new Router();

// Rute pentru căutare
$router->get('/', 'SearchController@index');
$router->post('/search/bulk', 'SearchController@bulkSearch');
$router->get('/search/export/csv', 'SearchController@exportCsv');
$router->get('/search/export/excel', 'SearchController@exportExcel');

// Rute pentru căutări simple
$router->post('/search/number', 'SearchController@searchByNumber');
$router->post('/search/party', 'SearchController@searchByParty');
$router->post('/search/object', 'SearchController@searchByObject');

// Rute pentru dosare
$router->get('/case/{numar}/{institutie}', 'Case<PERSON>ontroller@show');
$router->get('/case/{numar}/{institutie}/pdf', 'CaseController@generatePdf');
$router->get('/case/{numar}/{institutie}/pdf/inline', 'CaseController@showPdf');
$router->post('/case/send-email', 'CaseController@sendEmail');
$router->get('/case/{numar}/search-all', 'CaseController@searchInAllInstitutions');

// Rute pentru ședințe
$router->get('/case/{numar}/{institutie}/sessions', 'CaseController@getSessions');
$router->get('/case/{numar}/{institutie}/sessions/pdf', 'CaseController@generateSessionsPdf');

// Rute pentru căi de atac
$router->get('/case/{numar}/{institutie}/appeals', 'CaseController@getAppeals');

// Rute pentru pagini statice
$router->get('/about', 'PageController@about');
$router->get('/contact', 'PageController@contact');

// Rute pentru API
$router->get('/api/institutions', 'ApiController@getInstitutions');
$router->get('/api/search', 'ApiController@search');
$router->get('/api', 'SearchController@api');

// Handler pentru rutele negăsite
$router->setNotFoundHandler('PageController@notFound');

return $router; 