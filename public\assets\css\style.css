/*
 * Portal Judiciar - Stiluri principale
 */

/* Variabile */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --text-color: #333;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.15s ease-in-out;
}

/* Reset și base styles */
* {
    box-sizing: border-box;
}

/* Stiluri generale */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
    background-color: #f8f9fa;
    line-height: 1.6;
}

a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color 0.2s ease-in-out;
}

a:hover {
    color: #2980b9;
    text-decoration: underline;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    border-color: var(--primary-color);
}

/* Header */
header {
    box-shadow: var(--box-shadow);
}

header h1 {
    font-size: 1.75rem;
    font-weight: 600;
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 0.75rem 1.25rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    font-weight: 600;
}

.card-body {
    padding: 1.25rem;
}

/* Tabele */
.table {
    margin-bottom: 0;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Formulare */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Alerte */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* Footer */
footer {
    margin-top: auto;
}

footer h5 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

footer a {
    color: rgba(255, 255, 255, 0.8);
    transition: color 0.2s ease-in-out;
}

footer a:hover {
    color: #fff;
    text-decoration: none;
}

/* Breadcrumb */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
}

/* Butoane */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 0.5rem 1rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Tooltips */
.tooltip {
    font-size: 0.875rem;
}

/* Modal */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #f8f9fa;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background-color: #f8f9fa;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Stiluri pentru printare */
@media print {
    header, footer, .breadcrumb, .dropdown, .btn, .modal, .no-print {
        display: none !important;
    }

    body {
        background-color: #fff;
    }

    .card {
        box-shadow: none;
        border: 1px solid #000 !important;
        margin-bottom: 1rem;
    }

    .card-header {
        background-color: #f8f9fa !important;
        color: #000 !important;
    }

    .table {
        width: 100% !important;
    }

    .table th {
        background-color: #f8f9fa !important;
    }

    .container {
        max-width: 100%;
        width: 100%;
        padding: 0;
        margin: 0;
    }
}

/* Stiluri pentru notificări */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    width: 350px;
    max-width: 90%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

#notification {
    margin: 0;
    padding: 15px;
    border-radius: 4px;
    font-weight: 500;
}

/* Header și Navigation */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Buttons */
.btn-success {
    background: linear-gradient(135deg, var(--success-color), #1e7e34);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #c82333);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #e0a800);
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
}

/* Pagination */
.pagination .page-link {
    border: none;
    color: var(--primary-color);
    font-weight: 500;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Search form specific styles */
#searchForm {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border-radius: var(--border-radius);
}

#bulkSearchTerms {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    resize: vertical;
    min-height: 120px;
}

/* Quick search cards */
.quick-search-card {
    transition: var(--transition);
    cursor: pointer;
}

.quick-search-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.quick-search-card .card-body {
    text-align: center;
}

.quick-search-card i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

/* Statistics section */
.stat-item {
    text-align: center;
    padding: 1rem;
}

.stat-item h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

/* Results table */
.results-table {
    font-size: 0.9rem;
}

.results-table .btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* Case details specific styles */
.case-details .table td {
    vertical-align: middle;
}

.case-details .badge {
    font-size: 0.75rem;
}

/* Sidebar */
.sidebar-card {
    position: sticky;
    top: 1rem;
}

.sidebar-card .list-group-item {
    border: none;
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.sidebar-card .list-group-item:hover {
    background-color: var(--light-color);
    transform: translateX(5px);
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        margin-bottom: 0.5rem;
    }
    
    .table-responsive {
        font-size: 0.8rem;
    }
}

/* Accessibility improvements */
.btn:focus, .form-control:focus, .form-select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Utility classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.border-gradient {
    border: 2px solid;
    border-image: linear-gradient(135deg, var(--primary-color), var(--info-color)) 1;
}

.shadow-custom {
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1);
}

/* Dark mode support (future) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles can be added here */
}
