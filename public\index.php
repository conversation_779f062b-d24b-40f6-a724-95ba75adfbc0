<?php

/**
 * Portal Judiciar - Punctul de intrare în aplicație
 */

// Încărcăm bootstrap-ul aplicației
require_once dirname(__DIR__) . '/bootstrap.php';

// Configurăm raportarea erorilor în funcție de mediu
$config = \App\Config\AppConfig::getInstance();

if ($config->isDebug()) {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    ini_set('display_startup_errors', 0);
    error_reporting(0);
}

// Setăm timezone-ul
date_default_timezone_set($config->get('app.timezone'));

// Creăm directoarele necesare dacă nu există
$directories = [
    $config->getLogDir(),
    $config->getCacheDir(),
    $config->getTempDir(),
    $config->getLogDir() . '/archived'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Inițializăm sesiunea
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Configurăm logging-ul
$logDir = $config->getLogDir();
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// Încărcăm rutele
$router = require dirname(__DIR__) . '/routes.php';

// Procesăm cererea
try {
    $router->dispatch();
} catch (Exception $e) {
    // Logăm eroarea
    error_log('Eroare aplicație: ' . $e->getMessage());
    error_log('Stack trace: ' . $e->getTraceAsString());
    
    if ($config->isDebug()) {
        // În modul debug, afișăm eroarea
        echo '<h1>Eroare Aplicație</h1>';
        echo '<p><strong>Mesaj:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
        echo '<p><strong>Fișier:</strong> ' . htmlspecialchars($e->getFile()) . '</p>';
        echo '<p><strong>Linia:</strong> ' . $e->getLine() . '</p>';
        echo '<h2>Stack Trace:</h2>';
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    } else {
        // În producție, afișăm o pagină de eroare generică
        http_response_code(500);
        echo '<h1>Eroare Internă</h1>';
        echo '<p>Ne pare rău, a apărut o eroare. Vă rugăm să încercați din nou mai târziu.</p>';
        echo '<p><a href="/">← Înapoi la pagina principală</a></p>';
    }
}
