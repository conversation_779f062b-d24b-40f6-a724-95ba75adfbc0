{% extends "layouts/main.twig" %}

{% block title %}Dosar {{ dosar.numar }} - Detalii{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h2 text-primary">
                    <i class="fas fa-folder-open me-2"></i><PERSON><PERSON> {{ dosar.numar }}
                </h1>
                <p class="text-muted mb-0">{{ dosar.institutie }}</p>
            </div>
            <div class="btn-group" role="group">
                <a href="/case/{{ dosar.numar }}/{{ dosar.institutie }}/pdf" 
                   class="btn btn-danger" 
                   title="Descarcă PDF">
                    <i class="fas fa-file-pdf me-2"></i>PDF
                </a>
                <button class="btn btn-outline-primary" 
                        onclick="showEmailModal()" 
                        title="Trimite prin email">
                    <i class="fas fa-envelope me-2"></i>Email
                </button>
                <a href="/" class="btn btn-outline-secondary" title="Înapoi la căutare">
                    <i class="fas fa-arrow-left me-2"></i>Înapoi
                </a>
            </div>
        </div>

        <!-- Main Information -->
        <div class="row">
            <div class="col-md-8">
                <!-- Case Details Card -->
                <div class="card mb-4 border-0 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Informații Generale
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-muted">Număr Dosar</label>
                                    <p class="mb-0">{{ dosar.numar }}</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-muted">Instituție</label>
                                    <p class="mb-0">{{ dosar.institutie }}</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-muted">Data</label>
                                    <p class="mb-0">{{ dosar.data }}</p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-muted">Obiect</label>
                                    <p class="mb-0">{{ dosar.obiect }}</p>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-muted">Stadiu Procesual</label>
                                    <span class="badge bg-secondary">{{ dosar.stadiuProcesual }}</span>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold text-muted">Ultima Modificare</label>
                                    <p class="mb-0">{{ dosar.dataModificare }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Parties Section -->
                {% if dosar.parti %}
                <div class="card mb-4 border-0 shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-users me-2"></i>Părțile
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Nume</th>
                                        <th>Calitate</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for parte in dosar.parti %}
                                    <tr>
                                        <td>{{ parte.nume }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ parte.calitate }}</span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Sessions Section -->
                {% if dosar.sedinte %}
                <div class="card mb-4 border-0 shadow-sm">
                    <div class="card-header bg-warning text-dark">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-calendar-alt me-2"></i>Ședințe
                            </h5>
                            <a href="/case/{{ dosar.numar }}/{{ dosar.institutie }}/sessions/pdf" 
                               class="btn btn-sm btn-outline-dark">
                                <i class="fas fa-file-pdf me-1"></i>PDF Ședințe
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        {% for sedinta in dosar.sedinte %}
                        <div class="border-bottom pb-3 mb-3">
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>Data:</strong><br>
                                    <span class="text-muted">{{ sedinta.data }}</span>
                                </div>
                                <div class="col-md-2">
                                    <strong>Ora:</strong><br>
                                    <span class="text-muted">{{ sedinta.ora }}</span>
                                </div>
                                <div class="col-md-7">
                                    <strong>Complet:</strong><br>
                                    <span class="text-muted">{{ sedinta.complet }}</span>
                                </div>
                            </div>
                            {% if sedinta.solutie %}
                            <div class="row mt-2">
                                <div class="col-12">
                                    <strong>Soluție:</strong><br>
                                    <span class="text-muted">{{ sedinta.solutie }}</span>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Appeals Section -->
                {% if dosar.caiAtac %}
                <div class="card mb-4 border-0 shadow-sm">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-gavel me-2"></i>Căi de Atac
                        </h5>
                    </div>
                    <div class="card-body">
                        {% for caleAtac in dosar.caiAtac %}
                        <div class="border-bottom pb-3 mb-3">
                            <div class="row">
                                <div class="col-md-3">
                                    <strong>Data Declarare:</strong><br>
                                    <span class="text-muted">{{ caleAtac.dataDeclarare }}</span>
                                </div>
                                <div class="col-md-3">
                                    <strong>Parte Declaratoare:</strong><br>
                                    <span class="text-muted">{{ caleAtac.parteDeclaratoare }}</span>
                                </div>
                                <div class="col-md-3">
                                    <strong>Tip Cale Atac:</strong><br>
                                    <span class="badge bg-danger">{{ caleAtac.tipCaleAtac }}</span>
                                </div>
                                <div class="col-md-3">
                                    <strong>Număr Dosar Superior:</strong><br>
                                    <span class="text-muted">{{ caleAtac.numarDosarInstantaSuperior }}</span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>

            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Quick Actions -->
                <div class="card mb-4 border-0 shadow-sm">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>Acțiuni Rapide
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="/case/{{ dosar.numar }}/{{ dosar.institutie }}/pdf/inline" 
                               class="btn btn-outline-primary" 
                               target="_blank">
                                <i class="fas fa-eye me-2"></i>Vezi PDF
                            </a>
                            <button class="btn btn-outline-success" onclick="showEmailModal()">
                                <i class="fas fa-envelope me-2"></i>Trimite Email
                            </button>
                            <a href="/case/{{ dosar.numar }}/search-all" 
                               class="btn btn-outline-warning">
                                <i class="fas fa-search me-2"></i>Caută în Toate Instituțiile
                            </a>
                            <button class="btn btn-outline-secondary" onclick="copyCaseNumber('{{ dosar.numar }}')">
                                <i class="fas fa-copy me-2"></i>Copiază Numărul
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Case Statistics -->
                <div class="card mb-4 border-0 shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>Statistici
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-primary">{{ dosar.parti|length }}</h4>
                                <small class="text-muted">Părți</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-warning">{{ dosar.sedinte|length }}</h4>
                                <small class="text-muted">Ședințe</small>
                            </div>
                        </div>
                        <hr>
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-danger">{{ dosar.caiAtac|length }}</h4>
                                <small class="text-muted">Căi de Atac</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-success">{{ dosar.data|date('Y') }}</h4>
                                <small class="text-muted">An</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Related Links -->
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-dark text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-link me-2"></i>Link-uri Utile
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <a href="http://portal.just.ro" 
                               class="list-group-item list-group-item-action" 
                               target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>Portal Just
                            </a>
                            <a href="/about" class="list-group-item list-group-item-action">
                                <i class="fas fa-info-circle me-2"></i>Despre Aplicație
                            </a>
                            <a href="/contact" class="list-group-item list-group-item-action">
                                <i class="fas fa-envelope me-2"></i>Contact
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Email Modal -->
<div class="modal fade" id="emailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-envelope me-2"></i>Trimite Dosar prin Email
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="emailForm">
                    <input type="hidden" name="numar" value="{{ dosar.numar }}">
                    <input type="hidden" name="institutie" value="{{ dosar.institutie }}">
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Adresa de Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                        <div class="form-text">
                            Detaliile dosarului vor fi trimise ca atașament PDF.
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anulează</button>
                <button type="button" class="btn btn-primary" onclick="sendEmail()">
                    <i class="fas fa-paper-plane me-2"></i>Trimite
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showEmailModal() {
    new bootstrap.Modal(document.getElementById('emailModal')).show();
}

function sendEmail() {
    const form = document.getElementById('emailForm');
    const formData = new FormData(form);
    
    fetch('/case/send-email', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            showNotification(data.error, 'error');
        } else {
            showNotification('Email-ul a fost trimis cu succes!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('emailModal')).hide();
        }
    })
    .catch(error => {
        showNotification('Eroare la trimiterea email-ului', 'error');
    });
}

function copyCaseNumber(numar) {
    navigator.clipboard.writeText(numar).then(() => {
        showNotification('Numărul dosarului a fost copiat în clipboard', 'success');
    });
}

function showNotification(message, type) {
    const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
    const icon = type === 'error' ? 'exclamation-triangle' : 'check-circle';
    
    const alert = document.createElement('div');
    alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="fas fa-${icon} me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        alert.remove();
    }, 3000);
}
</script>
{% endblock %} 