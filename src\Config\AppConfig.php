<?php

namespace App\Config;

/**
 * Configurație unificată pentru aplicație
 */
class AppConfig
{
    private static ?AppConfig $instance = null;
    private array $config = [];

    private function __construct()
    {
        $this->loadConfig();
    }

    /**
     * Obține instanța singleton
     */
    public static function getInstance(): AppConfig
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Încarcă configurația
     */
    private function loadConfig(): void
    {
        $this->config = [
            // Configurații de bază
            'app' => [
                'name' => 'Portal Dosare Judecătorești',
                'version' => '1.0.0',
                'environment' => $this->getEnvironment(),
                'debug' => true,
                'timezone' => 'Europe/Bucharest',
                'locale' => 'ro_RO',
                'log_dir' => dirname(__DIR__, 2) . '/logs',
                'cache_dir' => dirname(__DIR__, 2) . '/cache',
                'temp_dir' => dirname(__DIR__, 2) . '/temp',
                'export_dir' => dirname(__DIR__, 2) . '/temp'
            ],

            // Configurații SOAP
            'soap' => [
                'wsdl' => 'http://portalquery.just.ro/query.asmx?WSDL',
                'endpoint' => 'http://portalquery.just.ro/query.asmx',
                'namespace' => 'portalquery.just.ro',
                'timeout' => 10,
                'max_retries' => 3,
                'retry_delay' => 500000
            ],

            // Configurații pentru afișare
            'display' => [
                'results_per_page' => 25,
                'max_search_results' => 1000,
                'date_format' => 'd.m.Y',
                'datetime_format' => 'd.m.Y H:i'
            ],

            // Configurații pentru cache
            'cache' => [
                'enabled' => true,
                'lifetime' => 3600, // 1 oră
                'prefix' => 'portal_'
            ],

            // Configurații pentru email
            'mail' => [
                'host' => $_ENV['MAIL_HOST'] ?? 'localhost',
                'port' => $_ENV['MAIL_PORT'] ?? 587,
                'username' => $_ENV['MAIL_USERNAME'] ?? '',
                'password' => $_ENV['MAIL_PASSWORD'] ?? '',
                'from_address' => $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>',
                'from_name' => $_ENV['MAIL_FROM_NAME'] ?? 'Portal Dosare Judecătorești',
                'encryption' => 'tls'
            ],

            // Configurații pentru PDF
            'pdf' => [
                'page_orientation' => 'P',
                'unit' => 'mm',
                'page_format' => 'A4',
                'margin_left' => 15,
                'margin_top' => 15,
                'margin_right' => 15,
                'margin_bottom' => 15,
                'header_margin' => 5,
                'footer_margin' => 10
            ],

            // Configurații pentru securitate
            'security' => [
                'session_lifetime' => 3600,
                'csrf_token_lifetime' => 1800,
                'max_login_attempts' => 5,
                'lockout_duration' => 900
            ]
        ];
    }

    /**
     * Obține o valoare de configurare
     */
    public function get(string $key, $default = null)
    {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $k) {
            if (!isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }

        return $value;
    }

    /**
     * Setează o valoare de configurare
     */
    public function set(string $key, $value): void
    {
        $keys = explode('.', $key);
        $config = &$this->config;

        foreach ($keys as $k) {
            if (!isset($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }

        $config = $value;
    }

    /**
     * Verifică dacă o cheie de configurare există
     */
    public function has(string $key): bool
    {
        return $this->get($key) !== null;
    }

    /**
     * Obține toate configurațiile
     */
    public function all(): array
    {
        return $this->config;
    }

    /**
     * Obține mediul de execuție
     */
    private function getEnvironment(): string
    {
        return $_ENV['APP_ENV'] ?? 'production';
    }

    /**
     * Verifică dacă aplicația rulează în modul debug
     */
    public function isDebug(): bool
    {
        return $this->get('app.debug', false);
    }

    /**
     * Verifică dacă aplicația rulează în producție
     */
    public function isProduction(): bool
    {
        return $this->getEnvironment() === 'production';
    }

    /**
     * Verifică dacă aplicația rulează în dezvoltare
     */
    public function isDevelopment(): bool
    {
        return $this->getEnvironment() === 'development';
    }

    /**
     * Obține calea către directorul de loguri
     */
    public function getLogDir(): string
    {
        return $this->get('app.log_dir');
    }

    /**
     * Obține calea către directorul de cache
     */
    public function getCacheDir(): string
    {
        return $this->get('app.cache_dir');
    }

    /**
     * Obține calea către directorul temporar
     */
    public function getTempDir(): string
    {
        return $this->get('app.temp_dir');
    }
} 