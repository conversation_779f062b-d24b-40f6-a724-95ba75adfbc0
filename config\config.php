<?php
/**
 * Configurații generale pentru aplicație
 * 
 * NOTĂ: Acest fișier este menținut pentru compatibilitate cu codul vechi.
 * Pentru cod nou, folosiți App\Config\AppConfig
 */

// Încărcăm noua configurație
require_once __DIR__ . '/../src/Config/AppConfig.php';

// Inițializăm configurația
$appConfig = new \App\Config\AppConfig();

// Definim constante pentru compatibilitate cu codul vechi
define('APP_NAME', $appConfig->get('app.name'));
define('SOAP_WSDL', $appConfig->get('soap.wsdl'));
define('SOAP_ENDPOINT', $appConfig->get('soap.endpoint'));
define('SOAP_NAMESPACE', $appConfig->get('soap.namespace'));
define('RESULTS_PER_PAGE', $appConfig->get('app.results_per_page', 25));
define('DATE_FORMAT', $appConfig->get('app.date_format', 'd.m.Y'));
define('DATETIME_FORMAT', $appConfig->get('app.datetime_format', 'd.m.Y H:i'));
define('DEBUG_MODE', $appConfig->get('app.debug', false));

// Funcție pentru afișarea erorilor în modul debug
function debug($data) {
    if (DEBUG_MODE) {
        echo '<pre>';
        print_r($data);
        echo '</pre>';
    }
}
