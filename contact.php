<?php
require_once 'includes/functions.php';
/**
 * Pa<PERSON>a de contact pentru Portal Judiciar
 * Portal Judiciar - Contact
 */

// Inițializare variabile pentru mesaje
$success_message = '';
$error_message = '';
$form_errors = [];

// Configurare email
$contact_email = '<EMAIL>';
$max_submissions_per_hour = 5; // Rate limiting

// Funcție pentru validarea email
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Funcție pentru rate limiting
function checkRateLimit($ip) {
    global $max_submissions_per_hour;
    
    $rate_limit_file = 'temp/rate_limit_' . md5($ip) . '.txt';
    $current_time = time();
    $submissions = [];
    
    // Creăm directorul temp dacă nu există
    if (!is_dir('temp')) {
        mkdir('temp', 0755, true);
    }
    
    // Citim submisiunile anterioare
    if (file_exists($rate_limit_file)) {
        $content = file_get_contents($rate_limit_file);
        $submissions = $content ? explode(',', $content) : [];
    }
    
    // Filtrăm submisiunile din ultima oră
    $submissions = array_filter($submissions, function($time) use ($current_time) {
        return ($current_time - $time) < 3600; // 1 oră = 3600 secunde
    });
    
    // Verificăm dacă am depășit limita
    if (count($submissions) >= $max_submissions_per_hour) {
        return false;
    }
    
    // Adăugăm submisiunea curentă
    $submissions[] = $current_time;
    file_put_contents($rate_limit_file, implode(',', $submissions));
    
    return true;
}

// Generare token CSRF
session_start();
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Procesare formular
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificare token CSRF
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $error_message = 'Eroare de securitate. Vă rugăm să reîncărcați pagina și să încercați din nou.';
    } else {
        // Verificare rate limiting
        $user_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        if (!checkRateLimit($user_ip)) {
            $error_message = 'Ați trimis prea multe mesaje în ultima oră. Vă rugăm să așteptați înainte de a trimite un nou mesaj.';
        } else {
            // Colectare și validare date
            $name = sanitizeInput($_POST['name'] ?? '');
            $email = sanitizeInput($_POST['email'] ?? '');
            $subject = sanitizeInput($_POST['subject'] ?? '');
            $message = sanitizeInput($_POST['message'] ?? '');
            
            // Validări
            if (empty($name)) {
                $form_errors['name'] = 'Numele este obligatoriu.';
            } elseif (strlen($name) < 2) {
                $form_errors['name'] = 'Numele trebuie să aibă cel puțin 2 caractere.';
            }
            
            if (empty($email)) {
                $form_errors['email'] = 'Adresa de email este obligatorie.';
            } elseif (!validateEmail($email)) {
                $form_errors['email'] = 'Adresa de email nu este validă.';
            }
            
            if (empty($subject)) {
                $form_errors['subject'] = 'Subiectul este obligatoriu.';
            } elseif (strlen($subject) < 5) {
                $form_errors['subject'] = 'Subiectul trebuie să aibă cel puțin 5 caractere.';
            }
            
            if (empty($message)) {
                $form_errors['message'] = 'Mesajul este obligatoriu.';
            } elseif (strlen($message) < 10) {
                $form_errors['message'] = 'Mesajul trebuie să aibă cel puțin 10 caractere.';
            } elseif (strlen($message) > 2000) {
                $form_errors['message'] = 'Mesajul nu poate depăși 2000 de caractere.';
            }

            // Additional spam checks
            $spam_keywords = ['viagra', 'casino', 'lottery', 'winner', 'congratulations', 'click here', 'free money'];
            $message_lower = strtolower($message);
            foreach ($spam_keywords as $keyword) {
                if (strpos($message_lower, $keyword) !== false) {
                    $form_errors['message'] = 'Mesajul conține conținut nepermis.';
                    break;
                }
            }

            // Check for excessive links
            if (substr_count($message, 'http') > 2) {
                $form_errors['message'] = 'Mesajul conține prea multe link-uri.';
            }
            
            // Dacă nu sunt erori, trimitem emailul
            if (empty($form_errors)) {
                $email_subject = "[Portal Judiciar] $subject";
                $email_body = "Mesaj nou de la Portal Judiciar\n\n";
                $email_body .= "Nume: $name\n";
                $email_body .= "Email: $email\n";
                $email_body .= "Subiect: $subject\n\n";
                $email_body .= "Mesaj:\n$message\n\n";
                $email_body .= "---\n";
                $email_body .= "Trimis de pe: {$_SERVER['HTTP_HOST']}\n";
                $email_body .= "IP: $user_ip\n";
                $email_body .= "Data: " . date('d.m.Y H:i:s') . "\n";
                
                $headers = "From: $email\r\n";
                $headers .= "Reply-To: $email\r\n";
                $headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
                $headers .= "X-Mailer: PHP/" . phpversion();
                
                // Try to send email
                $mail_sent = false;

                // Check if mail function is available
                if (function_exists('mail')) {
                    $mail_sent = mail($contact_email, $email_subject, $email_body, $headers);
                }

                if ($mail_sent) {
                    $success_message = 'Mesajul dumneavoastră a fost trimis cu succes! Vă vom răspunde în cel mai scurt timp posibil.';

                    // Log successful submission (optional)
                    error_log("Contact form submission from: $email, Subject: $subject");

                    // Reset form after successful submission
                    $name = $email = $subject = $message = '';

                    // Generate new CSRF token
                    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
                } else {
                    $error_message = 'A apărut o eroare la trimiterea mesajului. Vă rugăm să încercați din nou mai târziu sau să ne contactați direct.';

                    // Log failed submission
                    error_log("Failed to send contact form email from: $email");
                }
            }
        }
    }
}

require_once 'includes/config.php';

// Inițializare variabile pentru mesaje
$success_message = '';
$error_message = '';
$form_errors = [];

// Configurare email
$contact_email = '<EMAIL>';
$max_submissions_per_hour = 5; // Rate limiting

// Funcție pentru validarea email
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Funcție pentru rate limiting
function checkRateLimit($ip) {
    global $max_submissions_per_hour;
    
    $rate_limit_file = 'temp/rate_limit_' . md5($ip) . '.txt';
    $current_time = time();
    $submissions = [];
    
    // Creăm directorul temp dacă nu există
    if (!is_dir('temp')) {
        mkdir('temp', 0755, true);
    }
    
    // Citim submisiunile anterioare
    if (file_exists($rate_limit_file)) {
        $content = file_get_contents($rate_limit_file);
        $submissions = $content ? explode(',', $content) : [];
    }
    
    // Filtrăm submisiunile din ultima oră
    $submissions = array_filter($submissions, function($time) use ($current_time) {
        return ($current_time - $time) < 3600; // 1 oră = 3600 secunde
    });
    
    // Verificăm dacă am depășit limita
    if (count($submissions) >= $max_submissions_per_hour) {
        return false;
    }
    
    // Adăugăm submisiunea curentă
    $submissions[] = $current_time;
    file_put_contents($rate_limit_file, implode(',', $submissions));
    
    return true;
}

// Generare token CSRF
session_start();
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Procesare formular
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificare token CSRF
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $error_message = 'Eroare de securitate. Vă rugăm să reîncărcați pagina și să încercați din nou.';
    } else {
        // Verificare rate limiting
        $user_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        if (!checkRateLimit($user_ip)) {
            $error_message = 'Ați trimis prea multe mesaje în ultima oră. Vă rugăm să așteptați înainte de a trimite un nou mesaj.';
        } else {
            // Colectare și validare date
            $name = sanitizeInput($_POST['name'] ?? '');
            $email = sanitizeInput($_POST['email'] ?? '');
            $subject = sanitizeInput($_POST['subject'] ?? '');
            $message = sanitizeInput($_POST['message'] ?? '');
            
            // Validări
            if (empty($name)) {
                $form_errors['name'] = 'Numele este obligatoriu.';
            } elseif (strlen($name) < 2) {
                $form_errors['name'] = 'Numele trebuie să aibă cel puțin 2 caractere.';
            }
            
            if (empty($email)) {
                $form_errors['email'] = 'Adresa de email este obligatorie.';
            } elseif (!validateEmail($email)) {
                $form_errors['email'] = 'Adresa de email nu este validă.';
            }
            
            if (empty($subject)) {
                $form_errors['subject'] = 'Subiectul este obligatoriu.';
            } elseif (strlen($subject) < 5) {
                $form_errors['subject'] = 'Subiectul trebuie să aibă cel puțin 5 caractere.';
            }
            
            if (empty($message)) {
                $form_errors['message'] = 'Mesajul este obligatoriu.';
            } elseif (strlen($message) < 10) {
                $form_errors['message'] = 'Mesajul trebuie să aibă cel puțin 10 caractere.';
            }


// Set page-specific title and meta
$page_title = 'Contact - ' . APP_NAME;
$page_description = 'Contactați echipa Portal Judiciar pentru întrebări, sugestii sau asistență tehnică. Formular de contact securizat și rapid.';

require_once 'includes/header.php';
?>

<!-- Contact Page Specific Styles -->
<style>
.page-header {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 1rem;
}

.contact-form .card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.contact-form .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control.is-valid {
    border-color: #28a745;
}

.form-control.is-invalid {
    border-color: #dc3545;
}

.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    max-width: 400px;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #004085;
}

.text-danger {
    color: #dc3545 !important;
}

.me-1 {
    margin-right: 0.25rem !important;
}

.me-2 {
    margin-right: 0.5rem !important;
}

.ms-auto {
    margin-left: auto !important;
}

@media (max-width: 768px) {
    .notification-container {
        left: 20px;
        right: 20px;
        max-width: none;
    }

    .page-header h1 {
        font-size: 1.75rem;
    }

    .card-body {
        padding: 1rem;
    }
}
</style>

<!-- Notification Container -->
<div id="notificationContainer" class="notification-container" style="display: none;">
    <div id="notification" class="alert" role="alert"></div>
</div>

<!-- Navigation Bar -->
<nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
    <div class="container">
        <a class="navbar-brand" href="index.php">
            <i class="fas fa-gavel me-2"></i>
            DosareJust.ro - Portal Judiciar
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ms-auto">
                <li class="nav-item">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-search me-1"></i>
                        Căutare Dosare
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="sedinte.php">
                        <i class="fas fa-calendar-alt me-1"></i>
                        Ședințe
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="contact.php">
                        <i class="fas fa-envelope me-1"></i>
                        Contact
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container mt-4">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-header mb-4">
                <h1 class="h2">
                    <i class="fas fa-envelope me-2"></i>
                    Contact
                </h1>
                <p class="lead text-muted">
                    Aveți întrebări sau sugestii? Ne puteți contacta folosind formularul de mai jos.
                </p>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success_message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <strong>Succes!</strong> <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Închide">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Eroare!</strong> <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Închide">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <!-- Contact Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card contact-form">
                <div class="card-header">
                    <h3 class="h5 mb-0">
                        <i class="fas fa-paper-plane me-2"></i>
                        Trimiteți-ne un mesaj
                    </h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="contact.php" id="contactForm" novalidate>
                        <!-- CSRF Token -->
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        
                        <div class="row">
                            <!-- Nume -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    Nume <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control <?php echo isset($form_errors['name']) ? 'is-invalid' : ''; ?>" 
                                       id="name" 
                                       name="name" 
                                       value="<?php echo htmlspecialchars($name ?? ''); ?>"
                                       required 
                                       minlength="2"
                                       maxlength="100"
                                       placeholder="Introduceți numele dumneavoastră">
                                <?php if (isset($form_errors['name'])): ?>
                                <div class="invalid-feedback">
                                    <?php echo htmlspecialchars($form_errors['name']); ?>
                                </div>
                                <?php endif; ?>
                            </div>

                            <!-- Email -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    Adresa de email <span class="text-danger">*</span>
                                </label>
                                <input type="email" 
                                       class="form-control <?php echo isset($form_errors['email']) ? 'is-invalid' : ''; ?>" 
                                       id="email" 
                                       name="email" 
                                       value="<?php echo htmlspecialchars($email ?? ''); ?>"
                                       required 
                                       maxlength="255"
                                       placeholder="<EMAIL>">
                                <?php if (isset($form_errors['email'])): ?>
                                <div class="invalid-feedback">
                                    <?php echo htmlspecialchars($form_errors['email']); ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Subiect -->
                        <div class="mb-3">
                            <label for="subject" class="form-label">
                                <i class="fas fa-tag me-1"></i>
                                Subiect <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control <?php echo isset($form_errors['subject']) ? 'is-invalid' : ''; ?>" 
                                   id="subject" 
                                   name="subject" 
                                   value="<?php echo htmlspecialchars($subject ?? ''); ?>"
                                   required 
                                   minlength="5"
                                   maxlength="200"
                                   placeholder="Subiectul mesajului dumneavoastră">
                            <?php if (isset($form_errors['subject'])): ?>
                            <div class="invalid-feedback">
                                <?php echo htmlspecialchars($form_errors['subject']); ?>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Mesaj -->
                        <div class="mb-4">
                            <label for="message" class="form-label">
                                <i class="fas fa-comment me-1"></i>
                                Mesaj <span class="text-danger">*</span>
                            </label>
                            <textarea class="form-control <?php echo isset($form_errors['message']) ? 'is-invalid' : ''; ?>" 
                                      id="message" 
                                      name="message" 
                                      rows="6" 
                                      required 
                                      minlength="10"
                                      maxlength="2000"
                                      placeholder="Scrieți mesajul dumneavoastră aici..."><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                            <?php if (isset($form_errors['message'])): ?>
                            <div class="invalid-feedback">
                                <?php echo htmlspecialchars($form_errors['message']); ?>
                            </div>
                            <?php endif; ?>
                            <div class="form-text">
                                <span id="messageCounter">0</span>/2000 caractere
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Câmpurile marcate cu <span class="text-danger">*</span> sunt obligatorii
                            </div>
                            <div>
                                <button type="reset" class="btn btn-secondary me-2">
                                    <i class="fas fa-eraser me-1"></i>
                                    Resetează
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-1"></i>
                                    Trimite mesajul
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Information -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="h5 mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Informații de contact
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-globe me-2"></i>Portal Oficial</h6>
                            <p class="mb-3">
                                Pentru informații oficiale despre dosarele judecătorești, vizitați 
                                <a href="http://portal.just.ro" target="_blank" rel="noopener noreferrer">
                                    portal.just.ro <i class="fas fa-external-link-alt ms-1"></i>
                                </a>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-clock me-2"></i>Timp de răspuns</h6>
                            <p class="mb-3">
                                Vă vom răspunde în cel mai scurt timp posibil, de obicei în termen de 24-48 de ore în zilele lucrătoare.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for form validation and enhancements -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contactForm');
    const messageTextarea = document.getElementById('message');
    const messageCounter = document.getElementById('messageCounter');

    // Character counter for message field
    function updateMessageCounter() {
        const currentLength = messageTextarea.value.length;
        messageCounter.textContent = currentLength;

        if (currentLength > 1800) {
            messageCounter.style.color = '#dc3545'; // Bootstrap danger color
        } else if (currentLength > 1500) {
            messageCounter.style.color = '#ffc107'; // Bootstrap warning color
        } else {
            messageCounter.style.color = '#6c757d'; // Bootstrap muted color
        }
    }

    // Update counter on input
    messageTextarea.addEventListener('input', updateMessageCounter);

    // Initialize counter
    updateMessageCounter();

    // Form validation
    contactForm.addEventListener('submit', function(e) {
        let isValid = true;
        const formData = new FormData(contactForm);

        // Clear previous validation states
        const inputs = contactForm.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.classList.remove('is-invalid', 'is-valid');
        });

        // Validate name
        const name = formData.get('name').trim();
        const nameInput = document.getElementById('name');
        if (name.length < 2) {
            nameInput.classList.add('is-invalid');
            isValid = false;
        } else {
            nameInput.classList.add('is-valid');
        }

        // Validate email
        const email = formData.get('email').trim();
        const emailInput = document.getElementById('email');
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            emailInput.classList.add('is-invalid');
            isValid = false;
        } else {
            emailInput.classList.add('is-valid');
        }

        // Validate subject
        const subject = formData.get('subject').trim();
        const subjectInput = document.getElementById('subject');
        if (subject.length < 5) {
            subjectInput.classList.add('is-invalid');
            isValid = false;
        } else {
            subjectInput.classList.add('is-valid');
        }

        // Validate message
        const message = formData.get('message').trim();
        if (message.length < 10) {
            messageTextarea.classList.add('is-invalid');
            isValid = false;
        } else {
            messageTextarea.classList.add('is-valid');
        }

        if (!isValid) {
            e.preventDefault();

            // Show notification
            showNotification('Vă rugăm să corectați erorile din formular.', 'danger');

            // Scroll to first invalid field
            const firstInvalid = contactForm.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstInvalid.focus();
            }
        } else {
            // Show loading state
            const submitBtn = contactForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Se trimite...';
            submitBtn.disabled = true;

            // Re-enable button after a delay (in case of server error)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 10000);
        }
    });

    // Reset form handler
    contactForm.addEventListener('reset', function() {
        // Clear validation states
        const inputs = contactForm.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.classList.remove('is-invalid', 'is-valid');
        });

        // Reset counter
        updateMessageCounter();

        showNotification('Formularul a fost resetat.', 'info');
    });

    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        if (!alert.querySelector('.close')) {
            setTimeout(() => {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.remove();
                }, 500);
            }, 5000);
        }
    });
});

// Notification function
function showNotification(message, type = 'info') {
    const container = document.getElementById('notificationContainer');
    const notification = document.getElementById('notification');

    notification.className = `alert alert-${type} alert-dismissible fade show`;
    notification.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert" aria-label="Închide">
            <span aria-hidden="true">&times;</span>
        </button>
    `;

    container.style.display = 'block';

    // Auto-hide after 4 seconds
    setTimeout(() => {
        container.style.display = 'none';
    }, 4000);
}
</script>

</body>
</html>
