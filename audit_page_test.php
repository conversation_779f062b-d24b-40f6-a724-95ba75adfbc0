<?php
/**
 * Script de audit pentru testarea paginilor principale
 * Verifică dacă paginile se încarcă fără erori PHP
 */

// Configurare pentru capturarea erorilor
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Funcție pentru testarea unei pagini
function testPage($url, $pageName) {
    echo "\n=== Testare $pageName ===\n";
    echo "URL: $url\n";
    
    // Inițializare cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    curl_setopt($ch, CURLOPT_HEADER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ EROARE cURL: $error\n";
        return false;
    }
    
    echo "Status HTTP: $httpCode\n";
    
    // Separare header și body
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $header = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    // Verificare erori PHP în răspuns
    $phpErrors = [];
    if (preg_match_all('/(?:Fatal error|Parse error|Warning|Notice):\s*(.+)/i', $body, $matches)) {
        $phpErrors = $matches[0];
    }
    
    if (!empty($phpErrors)) {
        echo "❌ ERORI PHP DETECTATE:\n";
        foreach ($phpErrors as $error) {
            echo "   - $error\n";
        }
        return false;
    }
    
    // Verificare structură HTML de bază
    $hasDoctype = preg_match('/<!DOCTYPE\s+html/i', $body);
    $hasHtmlTag = preg_match('/<html[^>]*>/i', $body);
    $hasHeadTag = preg_match('/<head[^>]*>/i', $body);
    $hasBodyTag = preg_match('/<body[^>]*>/i', $body);
    $hasTitle = preg_match('/<title[^>]*>(.+?)<\/title>/i', $body, $titleMatch);
    
    echo "Structură HTML:\n";
    echo "  DOCTYPE: " . ($hasDoctype ? "✅" : "❌") . "\n";
    echo "  <html>: " . ($hasHtmlTag ? "✅" : "❌") . "\n";
    echo "  <head>: " . ($hasHeadTag ? "✅" : "❌") . "\n";
    echo "  <body>: " . ($hasBodyTag ? "✅" : "❌") . "\n";
    echo "  <title>: " . ($hasTitle ? "✅ (" . trim($titleMatch[1]) . ")" : "❌") . "\n";
    
    // Verificare meta tags importante
    $hasCharset = preg_match('/<meta[^>]+charset[^>]*>/i', $body);
    $hasViewport = preg_match('/<meta[^>]+viewport[^>]*>/i', $body);
    
    echo "Meta tags:\n";
    echo "  Charset: " . ($hasCharset ? "✅" : "❌") . "\n";
    echo "  Viewport: " . ($hasViewport ? "✅" : "❌") . "\n";
    
    // Verificare Bootstrap și CSS
    $hasBootstrap = preg_match('/bootstrap/i', $body);
    $hasFontAwesome = preg_match('/font-?awesome/i', $body);
    
    echo "Framework-uri:\n";
    echo "  Bootstrap: " . ($hasBootstrap ? "✅" : "❌") . "\n";
    echo "  Font Awesome: " . ($hasFontAwesome ? "✅" : "❌") . "\n";
    
    // Verificare culori tematice (albastru judiciar)
    $hasBlueTheme = preg_match('/#007bff|#2c3e50|primary|blue/i', $body);
    echo "  Temă albastră: " . ($hasBlueTheme ? "✅" : "❌") . "\n";
    
    if ($httpCode === 200 && empty($phpErrors) && $hasDoctype && $hasHtmlTag) {
        echo "✅ Pagina se încarcă corect\n";
        return true;
    } else {
        echo "❌ Pagina are probleme\n";
        return false;
    }
}

// Lista paginilor de testat
$baseUrl = 'http://localhost/just';
$pages = [
    'Pagina principală (nou)' => "$baseUrl/public/",
    'Căutare avansată (vechi)' => "$baseUrl/avansat.php",
    'Căutare în masă (vechi)' => "$baseUrl/avans.php",
    'Ședințe' => "$baseUrl/sedinte.php",
    'Detalii dosar (test)' => "$baseUrl/detalii_dosar.php?numar=1234/2023&institutie=TBBU",
];

echo "AUDIT PAGINI PORTAL JUDICIAR\n";
echo "============================\n";
echo "Data: " . date('Y-m-d H:i:s') . "\n";

$results = [];
foreach ($pages as $name => $url) {
    $results[$name] = testPage($url, $name);
}

echo "\n=== SUMAR REZULTATE ===\n";
$totalPages = count($results);
$successfulPages = array_sum($results);
$failedPages = $totalPages - $successfulPages;

echo "Total pagini testate: $totalPages\n";
echo "Pagini funcționale: $successfulPages ✅\n";
echo "Pagini cu probleme: $failedPages ❌\n";

if ($failedPages > 0) {
    echo "\nPagini cu probleme:\n";
    foreach ($results as $page => $success) {
        if (!$success) {
            echo "  - $page\n";
        }
    }
}

echo "\n=== RECOMANDĂRI ===\n";
echo "1. Verificați log-urile PHP pentru erori detaliate\n";
echo "2. Testați manual în browser pentru verificare vizuală\n";
echo "3. Verificați responsive design pe diferite dimensiuni\n";
echo "4. Testați funcționalitatea formularelor\n";

?>
