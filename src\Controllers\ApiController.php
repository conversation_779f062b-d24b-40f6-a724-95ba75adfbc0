<?php

namespace App\Controllers;

use App\Services\DosarService;

/**
 * Controller pentru API-ul REST
 */
class ApiController extends BaseController
{
    private DosarService $dosarService;

    public function __construct()
    {
        parent::__construct();
        $this->dosarService = new DosarService();
    }

    /**
     * Obține lista instituțiilor
     */
    public function getInstitutions(): void
    {
        try {
            $institutions = $this->getInstanteList();
            
            $this->json([
                'success' => true,
                'data' => $institutions,
                'count' => count($institutions),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        } catch (\Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Căutare API
     */
    public function search(): void
    {
        $getData = $this->getGetData();
        
        $type = $getData['type'] ?? '';
        $query = $getData['q'] ?? '';
        $institution = $getData['institution'] ?? '';
        $limit = min((int)($getData['limit'] ?? 50), 100);
        $page = max((int)($getData['page'] ?? 1), 1);
        
        if (empty($query)) {
            $this->json([
                'success' => false,
                'error' => 'Parametrul "q" este obligatoriu'
            ], 400);
            return;
        }

        try {
            $results = [];
            
            switch ($type) {
                case 'number':
                    $results = $this->dosarService->cautareDupaNumarDosar($query, $institution);
                    break;
                case 'party':
                    $results = $this->dosarService->cautareDupaNumeParte($query, $institution);
                    break;
                case 'object':
                    $results = $this->dosarService->cautareDupaObiect($query, $institution);
                    break;
                default:
                    // Auto-detect type
                    $results = $this->autoDetectSearch($query, $institution);
                    break;
            }

            // Pagination
            $total = count($results);
            $offset = ($page - 1) * $limit;
            $paginatedResults = array_slice($results, $offset, $limit);

            $this->json([
                'success' => true,
                'data' => $this->formatResults($paginatedResults),
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ],
                'query' => [
                    'type' => $type,
                    'q' => $query,
                    'institution' => $institution
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obține detaliile unui dosar
     */
    public function getCase(string $numar, string $institutie): void
    {
        try {
            $dosar = $this->dosarService->getDetaliiDosar($numar, $institutie);
            
            if (empty($dosar)) {
                $this->json([
                    'success' => false,
                    'error' => 'Dosarul nu a fost găsit'
                ], 404);
                return;
            }

            $this->json([
                'success' => true,
                'data' => $this->formatCaseDetails($dosar),
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Caută dosar în toate instituțiile
     */
    public function searchAllInstitutions(string $numar): void
    {
        try {
            $results = $this->dosarService->searchDosarInAllInstitutions($numar);
            
            $this->json([
                'success' => true,
                'data' => $this->formatResults($results),
                'count' => count($results),
                'query' => $numar,
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obține ședințele pentru un dosar
     */
    public function getSessions(string $numar, string $institutie): void
    {
        try {
            $dosar = $this->dosarService->getDetaliiDosar($numar, $institutie);
            
            if (empty($dosar)) {
                $this->json([
                    'success' => false,
                    'error' => 'Dosarul nu a fost găsit'
                ], 404);
                return;
            }

            $sessions = $dosar->sedinte ?? [];
            
            $this->json([
                'success' => true,
                'data' => $this->formatSessions($sessions),
                'count' => count($sessions),
                'case' => [
                    'number' => $numar,
                    'institution' => $institutie
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obține căile de atac pentru un dosar
     */
    public function getAppeals(string $numar, string $institutie): void
    {
        try {
            $dosar = $this->dosarService->getDetaliiDosar($numar, $institutie);
            
            if (empty($dosar)) {
                $this->json([
                    'success' => false,
                    'error' => 'Dosarul nu a fost găsit'
                ], 404);
                return;
            }

            $appeals = $dosar->caiAtac ?? [];
            
            $this->json([
                'success' => true,
                'data' => $this->formatAppeals($appeals),
                'count' => count($appeals),
                'case' => [
                    'number' => $numar,
                    'institution' => $institutie
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Statistici API
     */
    public function getStats(): void
    {
        try {
            $stats = [
                'total_institutions' => count($this->getInstanteList()),
                'api_version' => '1.0.0',
                'last_updated' => date('Y-m-d H:i:s'),
                'endpoints' => [
                    'GET /api/institutions' => 'Lista instituțiilor',
                    'GET /api/search' => 'Căutare dosare',
                    'GET /api/case/{numar}/{institutie}' => 'Detalii dosar',
                    'GET /api/case/{numar}/search-all' => 'Căutare în toate instituțiile',
                    'GET /api/case/{numar}/{institutie}/sessions' => 'Ședințe dosar',
                    'GET /api/case/{numar}/{institutie}/appeals' => 'Căi de atac',
                    'GET /api/stats' => 'Statistici API'
                ]
            ];

            $this->json([
                'success' => true,
                'data' => $stats,
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    // Metode helper private

    private function autoDetectSearch(string $query, string $institution): array
    {
        // Detectează automat tipul de căutare
        if (preg_match('/^\d+\/\d+(?:\/\d+)?$/', $query)) {
            return $this->dosarService->cautareDupaNumarDosar($query, $institution);
        }
        
        // Verifică dacă pare să fie un obiect de dosar
        $objectKeywords = ['divorț', 'executare', 'faliment', 'contencios', 'penal', 'civil'];
        foreach ($objectKeywords as $keyword) {
            if (stripos($query, $keyword) !== false) {
                return $this->dosarService->cautareDupaObiect($query, $institution);
            }
        }
        
        // Implicit, caută după nume parte
        return $this->dosarService->cautareDupaNumeParte($query, $institution);
    }

    private function formatResults(array $results): array
    {
        $formatted = [];
        foreach ($results as $result) {
            $formatted[] = [
                'number' => $result->numar ?? '',
                'institution' => $result->institutie ?? '',
                'date' => $result->data ?? '',
                'object' => $result->obiect ?? '',
                'status' => $result->stadiuProcesual ?? '',
                'category' => $result->categorieCaz ?? '',
                'last_modified' => $result->dataModificare ?? '',
                'parties_count' => count($result->parti ?? []),
                'sessions_count' => count($result->sedinte ?? []),
                'appeals_count' => count($result->caiAtac ?? [])
            ];
        }
        return $formatted;
    }

    private function formatCaseDetails(object $dosar): array
    {
        return [
            'number' => $dosar->numar ?? '',
            'institution' => $dosar->institutie ?? '',
            'date' => $dosar->data ?? '',
            'object' => $dosar->obiect ?? '',
            'status' => $dosar->stadiuProcesual ?? '',
            'category' => $dosar->categorieCaz ?? '',
            'last_modified' => $dosar->dataModificare ?? '',
            'parties' => $this->formatParties($dosar->parti ?? []),
            'sessions' => $this->formatSessions($dosar->sedinte ?? []),
            'appeals' => $this->formatAppeals($dosar->caiAtac ?? [])
        ];
    }

    private function formatParties(array $parties): array
    {
        $formatted = [];
        foreach ($parties as $party) {
            $formatted[] = [
                'name' => $party->nume ?? '',
                'role' => $party->calitate ?? ''
            ];
        }
        return $formatted;
    }

    private function formatSessions(array $sessions): array
    {
        $formatted = [];
        foreach ($sessions as $session) {
            $formatted[] = [
                'date' => $session->data ?? '',
                'time' => $session->ora ?? '',
                'panel' => $session->complet ?? '',
                'decision' => $session->solutie ?? ''
            ];
        }
        return $formatted;
    }

    private function formatAppeals(array $appeals): array
    {
        $formatted = [];
        foreach ($appeals as $appeal) {
            $formatted[] = [
                'declaration_date' => $appeal->dataDeclarare ?? '',
                'declaring_party' => $appeal->parteDeclaratoare ?? '',
                'appeal_type' => $appeal->tipCaleAtac ?? '',
                'superior_case_number' => $appeal->numarDosarInstantaSuperior ?? ''
            ];
        }
        return $formatted;
    }

    private function getInstanteList(): array
    {
        return [
            'JudecatoriaSECTORUL1BUCURESTI' => 'Judecătoria Sectorului 1 București',
            'JudecatoriaSECTORUL2BUCURESTI' => 'Judecătoria Sectorului 2 București',
            'JudecatoriaSECTORUL3BUCURESTI' => 'Judecătoria Sectorului 3 București',
            'JudecatoriaSECTORUL4BUCURESTI' => 'Judecătoria Sectorului 4 București',
            'JudecatoriaSECTORUL5BUCURESTI' => 'Judecătoria Sectorului 5 București',
            'JudecatoriaSECTORUL6BUCURESTI' => 'Judecătoria Sectorului 6 București',
            'TribunalulBUCURESTI' => 'Tribunalul București',
            'CurteadeApelBUCURESTI' => 'Curtea de Apel București',
            'InaltaCurtedeCasatiesiJustitie' => 'Înalta Curte de Casație și Justiție'
        ];
    }
} 