<?php

/**
 * <PERSON><PERSON>ier bootstrap pentru inițializarea aplicației
 */

// Încărcăm autoloader-ul Composer
require_once __DIR__ . '/vendor/autoload.php';

// Încărcăm serviciile necesare
require_once __DIR__ . '/services/SedinteService.php';

// Încărcăm funcțiile utilitare
require_once __DIR__ . '/includes/functions.php';

// Încărcăm configurația unificată
$config = \App\Config\AppConfig::getInstance();

// Configurăm raportarea erorilor în funcție de mediu
if ($config->isDebug()) {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    ini_set('display_startup_errors', 0);
    error_reporting(0);
}

// Configurăm logging-ul
ini_set('log_errors', 1);
ini_set('error_log', $config->getLogDir() . '/php_errors.log');

// Setăm timezone-ul
date_default_timezone_set($config->get('app.timezone'));

// Setăm locale-ul
setlocale(LC_ALL, $config->get('app.locale'));

// Configurăm TCPDF
define('K_TCPDF_EXTERNAL_CONFIG', true);

// Creăm directoarele necesare dacă nu există
$directories = [
    $config->getLogDir(),
    $config->getCacheDir(),
    $config->getTempDir(),
    $config->getLogDir() . '/archived'
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// Inițializăm sesiunea
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Configurăm handler-ul pentru erori fatale
register_shutdown_function(function() use ($config) {
    $error = error_get_last();
    if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        error_log('Fatal Error: ' . $error['message'] . ' in ' . $error['file'] . ' on line ' . $error['line']);
        
        if ($config->isDebug()) {
            echo '<h1>Eroare Fatală</h1>';
            echo '<p><strong>Mesaj:</strong> ' . htmlspecialchars($error['message']) . '</p>';
            echo '<p><strong>Fișier:</strong> ' . htmlspecialchars($error['file']) . '</p>';
            echo '<p><strong>Linia:</strong> ' . $error['line'] . '</p>';
        } else {
            http_response_code(500);
            echo '<h1>Eroare Internă</h1>';
            echo '<p>Ne pare rău, a apărut o eroare. Vă rugăm să încercați din nou mai târziu.</p>';
        }
    }
});

// Configurăm handler-ul pentru excepții neprinse
set_exception_handler(function($exception) use ($config) {
    error_log('Uncaught Exception: ' . $exception->getMessage());
    error_log('Stack trace: ' . $exception->getTraceAsString());
    
    if ($config->isDebug()) {
        echo '<h1>Excepție Neprinsă</h1>';
        echo '<p><strong>Mesaj:</strong> ' . htmlspecialchars($exception->getMessage()) . '</p>';
        echo '<p><strong>Fișier:</strong> ' . htmlspecialchars($exception->getFile()) . '</p>';
        echo '<p><strong>Linia:</strong> ' . $exception->getLine() . '</p>';
        echo '<h2>Stack Trace:</h2>';
        echo '<pre>' . htmlspecialchars($exception->getTraceAsString()) . '</pre>';
    } else {
        http_response_code(500);
        echo '<h1>Eroare Internă</h1>';
        echo '<p>Ne pare rău, a apărut o eroare. Vă rugăm să încercați din nou mai târziu.</p>';
    }
});
