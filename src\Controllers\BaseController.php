<?php

namespace App\Controllers;

use App\Helpers\TemplateEngine;

/**
 * Controller de bază cu funcționalități comune
 */
abstract class BaseController
{
    protected TemplateEngine $templateEngine;

    public function __construct()
    {
        $this->templateEngine = new TemplateEngine();
    }

    /**
     * Redirecționează către o URL
     */
    protected function redirect(string $url): void
    {
        header("Location: {$url}");
        exit;
    }

    /**
     * Returnează răspuns JSON
     */
    protected function json(array $data, int $statusCode = 200): void
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
    }

    /**
     * Validează datele de intrare
     */
    protected function validate(array $data, array $rules): array
    {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            if (!isset($data[$field]) || empty($data[$field])) {
                if (strpos($rule, 'required') !== false) {
                    $errors[$field] = "Câmpul {$field} este obligatoriu";
                }
                continue;
            }

            $value = $data[$field];

            if (strpos($rule, 'email') !== false && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                $errors[$field] = "Adresa de email nu este validă";
            }

            if (strpos($rule, 'min:') !== false) {
                preg_match('/min:(\d+)/', $rule, $matches);
                $min = (int)$matches[1];
                if (strlen($value) < $min) {
                    $errors[$field] = "Câmpul {$field} trebuie să aibă minim {$min} caractere";
                }
            }

            if (strpos($rule, 'max:') !== false) {
                preg_match('/max:(\d+)/', $rule, $matches);
                $max = (int)$matches[1];
                if (strlen($value) > $max) {
                    $errors[$field] = "Câmpul {$field} trebuie să aibă maxim {$max} caractere";
                }
            }
        }

        return $errors;
    }

    /**
     * Curăță datele de intrare
     */
    protected function sanitize(array $data): array
    {
        $clean = [];
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                $clean[$key] = trim(strip_tags($value));
            } else {
                $clean[$key] = $value;
            }
        }
        return $clean;
    }

    /**
     * Obține datele POST
     */
    protected function getPostData(): array
    {
        return $this->sanitize($_POST);
    }

    /**
     * Obține datele GET
     */
    protected function getGetData(): array
    {
        return $this->sanitize($_GET);
    }
} 