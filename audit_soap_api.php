<?php
/**
 * Script de audit pentru testarea integrării SOAP API
 * Testează operațiunile CautareDosare și CautareSedinte
 */

// Configurare pentru capturarea erorilor
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Încărcăm configurația
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "AUDIT SOAP API INTEGRATION\n";
echo "==========================\n";
echo "Data: " . date('Y-m-d H:i:s') . "\n\n";

// Verificare extensie SOAP
if (!extension_loaded('soap')) {
    echo "❌ EROARE CRITICĂ: Extensia SOAP nu este încărcată!\n";
    exit(1);
}
echo "✅ Extensia SOAP este disponibilă\n";

// Verificare configurație SOAP
echo "\n=== CONFIGURAȚIE SOAP ===\n";
echo "WSDL: " . SOAP_WSDL . "\n";
echo "Endpoint: " . SOAP_ENDPOINT . "\n";
echo "Namespace: " . SOAP_NAMESPACE . "\n";

// Test conectivitate WSDL
echo "\n=== TEST CONECTIVITATE WSDL ===\n";
try {
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ]);
    
    $wsdlContent = file_get_contents(SOAP_WSDL, false, $context);
    if ($wsdlContent !== false) {
        echo "✅ WSDL este accesibil (" . strlen($wsdlContent) . " bytes)\n";
    } else {
        echo "❌ WSDL nu poate fi accesat\n";
    }
} catch (Exception $e) {
    echo "❌ Eroare la accesarea WSDL: " . $e->getMessage() . "\n";
}

// Test inițializare client SOAP
echo "\n=== TEST INIȚIALIZARE CLIENT SOAP ===\n";
try {
    $soapOptions = [
        'trace' => true,
        'exceptions' => true,
        'connection_timeout' => 10,
        'cache_wsdl' => WSDL_CACHE_NONE,
        'user_agent' => 'Portal Judiciar Romania'
    ];
    
    $soapClient = new SoapClient(SOAP_WSDL, $soapOptions);
    echo "✅ Client SOAP inițializat cu succes\n";
    
    // Afișare funcții disponibile
    $functions = $soapClient->__getFunctions();
    echo "Funcții SOAP disponibile:\n";
    foreach ($functions as $function) {
        echo "  - " . $function . "\n";
    }
    
} catch (SoapFault $e) {
    echo "❌ Eroare SOAP la inițializare: " . $e->getMessage() . "\n";
    $soapClient = null;
} catch (Exception $e) {
    echo "❌ Eroare generală la inițializare: " . $e->getMessage() . "\n";
    $soapClient = null;
}

if ($soapClient === null) {
    echo "\n❌ Nu se poate continua testarea fără client SOAP funcțional\n";
    exit(1);
}

// Test CautareDosare
echo "\n=== TEST CAUTAREDOSARE ===\n";

$testCases = [
    [
        'name' => 'Căutare după număr dosar',
        'params' => [
            'numarDosar' => '1234/2023',
            'numePersoana' => '',
            'obiectDosar' => '',
            'institutie' => null
        ]
    ],
    [
        'name' => 'Căutare după nume persoană',
        'params' => [
            'numarDosar' => '',
            'numePersoana' => 'Popescu',
            'obiectDosar' => '',
            'institutie' => null
        ]
    ],
    [
        'name' => 'Căutare după obiect dosar',
        'params' => [
            'numarDosar' => '',
            'numePersoana' => '',
            'obiectDosar' => 'divorț',
            'institutie' => null
        ]
    ],
    [
        'name' => 'Căutare cu instituție specifică',
        'params' => [
            'numarDosar' => '1234/2023',
            'numePersoana' => '',
            'obiectDosar' => '',
            'institutie' => 'TBBU'
        ]
    ]
];

foreach ($testCases as $testCase) {
    echo "\n--- " . $testCase['name'] . " ---\n";
    echo "Parametri: " . json_encode($testCase['params'], JSON_UNESCAPED_UNICODE) . "\n";
    
    try {
        $startTime = microtime(true);
        $response = $soapClient->CautareDosare($testCase['params']);
        $endTime = microtime(true);
        $duration = round(($endTime - $startTime) * 1000, 2);
        
        echo "✅ Răspuns primit în {$duration}ms\n";
        
        if (isset($response->CautareDosar)) {
            $results = $response->CautareDosar;
            if (is_array($results)) {
                echo "Numărul de rezultate: " . count($results) . "\n";
                if (count($results) > 0) {
                    echo "Primul rezultat: " . json_encode($results[0], JSON_UNESCAPED_UNICODE) . "\n";
                }
            } else {
                echo "Un singur rezultat: " . json_encode($results, JSON_UNESCAPED_UNICODE) . "\n";
            }
        } else {
            echo "Structura răspunsului: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n";
        }
        
    } catch (SoapFault $e) {
        echo "❌ Eroare SOAP: " . $e->getMessage() . "\n";
        echo "Fault Code: " . $e->faultcode . "\n";
        if (isset($e->detail)) {
            echo "Detalii: " . print_r($e->detail, true) . "\n";
        }
    } catch (Exception $e) {
        echo "❌ Eroare generală: " . $e->getMessage() . "\n";
    }
}

// Test CautareSedinte
echo "\n=== TEST CAUTARESEDINTE ===\n";

$sedinteTestCases = [
    [
        'name' => 'Căutare ședințe fără filtre',
        'params' => [
            'institutie' => null,
            'dataStart' => '',
            'dataEnd' => ''
        ]
    ],
    [
        'name' => 'Căutare ședințe cu interval de date',
        'params' => [
            'institutie' => null,
            'dataStart' => '2023-01-01T00:00:00',
            'dataEnd' => '2023-12-31T23:59:59'
        ]
    ],
    [
        'name' => 'Căutare ședințe pentru instituție specifică',
        'params' => [
            'institutie' => 'TBBU',
            'dataStart' => '',
            'dataEnd' => ''
        ]
    ]
];

foreach ($sedinteTestCases as $testCase) {
    echo "\n--- " . $testCase['name'] . " ---\n";
    echo "Parametri: " . json_encode($testCase['params'], JSON_UNESCAPED_UNICODE) . "\n";
    
    try {
        $startTime = microtime(true);
        $response = $soapClient->CautareSedinte($testCase['params']);
        $endTime = microtime(true);
        $duration = round(($endTime - $startTime) * 1000, 2);
        
        echo "✅ Răspuns primit în {$duration}ms\n";
        
        if (isset($response->CautareSedinta)) {
            $results = $response->CautareSedinta;
            if (is_array($results)) {
                echo "Numărul de rezultate: " . count($results) . "\n";
                if (count($results) > 0) {
                    echo "Primul rezultat: " . json_encode($results[0], JSON_UNESCAPED_UNICODE) . "\n";
                }
            } else {
                echo "Un singur rezultat: " . json_encode($results, JSON_UNESCAPED_UNICODE) . "\n";
            }
        } else {
            echo "Structura răspunsului: " . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n";
        }
        
    } catch (SoapFault $e) {
        echo "❌ Eroare SOAP: " . $e->getMessage() . "\n";
        echo "Fault Code: " . $e->faultcode . "\n";
        if (isset($e->detail)) {
            echo "Detalii: " . print_r($e->detail, true) . "\n";
        }
    } catch (Exception $e) {
        echo "❌ Eroare generală: " . $e->getMessage() . "\n";
    }
}

echo "\n=== SUMAR AUDIT SOAP API ===\n";
echo "Testarea SOAP API completă.\n";
echo "Verificați rezultatele de mai sus pentru identificarea problemelor.\n";
?>
