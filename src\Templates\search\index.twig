{% extends "layouts/main.twig" %}

{% block title %}Căutare Dosare - Portal Dosare Judecătorești{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <!-- Header Section -->
        <div class="text-center mb-5">
            <h1 class="display-4 text-primary mb-3">
                <i class="fas fa-search me-3"></i>Căutare Dosare
            </h1>
            <p class="lead text-muted">
                Căutați dosare judecătorești din România folosind multiple criterii
            </p>
        </div>

        <!-- Search Form -->
        <div class="card shadow-lg border-0">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>Căutare Avansată
                </h5>
            </div>
            <div class="card-body p-4">
                <form method="POST" action="/search/bulk" id="searchForm">
                    <!-- Bulk Search Section -->
                    <div class="mb-4">
                        <label for="bulkSearchTerms" class="form-label fw-bold">
                            <i class="fas fa-list me-2"></i>Căutare în Masă
                        </label>
                        <textarea 
                            class="form-control" 
                            id="bulkSearchTerms" 
                            name="bulkSearchTerms" 
                            rows="6" 
                            placeholder="Introduceți termenii de căutare (câte unul pe linie):&#10;Exemple:&#10;1234/2023&#10;Ion Popescu&#10;divorț&#10;executare silită"
                        ></textarea>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Puteți introduce numere de dosar, nume de părți sau obiecte de dosar. 
                            Sistemul detectează automat tipul de căutare.
                        </div>
                    </div>

                    <!-- Advanced Filters -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="institutie" class="form-label fw-bold">
                                <i class="fas fa-building me-2"></i>Instituție
                            </label>
                            <select class="form-select" id="institutie" name="institutie">
                                <option value="">Toate instituțiile</option>
                                {% for cod, nume in instante %}
                                    <option value="{{ cod }}">{{ nume }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="categorieInstanta" class="form-label fw-bold">
                                <i class="fas fa-gavel me-2"></i>Categorie Instanță
                            </label>
                            <select class="form-select" id="categorieInstanta" name="categorieInstanta">
                                <option value="">Toate categoriile</option>
                                <option value="judecatorie">Judecătorie</option>
                                <option value="tribunal">Tribunal</option>
                                <option value="curte_apel">Curte de Apel</option>
                                <option value="curte_suprema">Curte Supremă</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="categorieCaz" class="form-label fw-bold">
                                <i class="fas fa-folder me-2"></i>Categorie Caz
                            </label>
                            <select class="form-select" id="categorieCaz" name="categorieCaz">
                                <option value="">Toate categoriile</option>
                                <option value="civil">Civil</option>
                                <option value="penal">Penal</option>
                                <option value="comercial">Comercial</option>
                                <option value="administrativ">Administrativ</option>
                                <option value="fiscal">Fiscal</option>
                                <option value="munca">Muncă</option>
                                <option value="familie">Familie</option>
                                <option value="faliment">Faliment</option>
                                <option value="executare">Executare</option>
                            </select>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="dataInceput" class="form-label fw-bold">
                                <i class="fas fa-calendar me-2"></i>Data Început
                            </label>
                            <input type="date" class="form-control" id="dataInceput" name="dataInceput">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="dataSfarsit" class="form-label fw-bold">
                                <i class="fas fa-calendar me-2"></i>Data Sfârșit
                            </label>
                            <input type="date" class="form-control" id="dataSfarsit" name="dataSfarsit">
                        </div>

                        <div class="col-md-6 mb-3 d-flex align-items-end">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="exactMatch" name="exactMatch">
                                <label class="form-check-label" for="exactMatch">
                                    <i class="fas fa-crosshairs me-2"></i>Căutare exactă
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="reset" class="btn btn-outline-secondary me-md-2">
                            <i class="fas fa-undo me-2"></i>Resetează
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Caută
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quick Search Options -->
        <div class="row mt-5">
            <div class="col-md-4 mb-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-hashtag fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Căutare după Număr</h5>
                        <p class="card-text">Căutați dosare folosind numărul exact al dosarului.</p>
                        <button class="btn btn-outline-primary btn-sm" onclick="showNumberSearch()">
                            <i class="fas fa-search me-1"></i>Caută
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-user fa-3x text-success mb-3"></i>
                        <h5 class="card-title">Căutare după Parte</h5>
                        <p class="card-text">Căutați dosare în care apare o anumită parte.</p>
                        <button class="btn btn-outline-success btn-sm" onclick="showPartySearch()">
                            <i class="fas fa-search me-1"></i>Caută
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="fas fa-folder-open fa-3x text-warning mb-3"></i>
                        <h5 class="card-title">Căutare după Obiect</h5>
                        <p class="card-text">Căutați dosare după obiectul sau tipul cauzei.</p>
                        <button class="btn btn-outline-warning btn-sm" onclick="showObjectSearch()">
                            <i class="fas fa-search me-1"></i>Caută
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Section -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card border-0 bg-light">
                    <div class="card-body text-center">
                        <h5 class="card-title text-muted">
                            <i class="fas fa-chart-bar me-2"></i>Statistici
                        </h5>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h3 class="text-primary">1000+</h3>
                                    <small class="text-muted">Dosare zilnic</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h3 class="text-success">50+</h3>
                                    <small class="text-muted">Instituții</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h3 class="text-warning">24/7</h3>
                                    <small class="text-muted">Disponibilitate</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stat-item">
                                    <h3 class="text-info">99.9%</h3>
                                    <small class="text-muted">Precizie</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Search Modals -->
<div class="modal fade" id="numberSearchModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-hashtag me-2"></i>Căutare după Număr Dosar
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="numberSearchForm">
                    <div class="mb-3">
                        <label for="numarDosar" class="form-label">Număr Dosar</label>
                        <input type="text" class="form-control" id="numarDosar" name="numarDosar" 
                               placeholder="Ex: 1234/2023" required>
                    </div>
                    <div class="mb-3">
                        <label for="institutieNumar" class="form-label">Instituție (opțional)</label>
                        <select class="form-select" id="institutieNumar" name="institutie">
                            <option value="">Toate instituțiile</option>
                            {% for cod, nume in instante %}
                                <option value="{{ cod }}">{{ nume }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anulează</button>
                <button type="button" class="btn btn-primary" onclick="searchByNumber()">
                    <i class="fas fa-search me-2"></i>Caută
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="partySearchModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user me-2"></i>Căutare după Nume Parte
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="partySearchForm">
                    <div class="mb-3">
                        <label for="numeParte" class="form-label">Nume Parte</label>
                        <input type="text" class="form-control" id="numeParte" name="numeParte" 
                               placeholder="Ex: Ion Popescu" required>
                    </div>
                    <div class="mb-3">
                        <label for="institutieParte" class="form-label">Instituție (opțional)</label>
                        <select class="form-select" id="institutieParte" name="institutie">
                            <option value="">Toate instituțiile</option>
                            {% for cod, nume in instante %}
                                <option value="{{ cod }}">{{ nume }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anulează</button>
                <button type="button" class="btn btn-success" onclick="searchByParty()">
                    <i class="fas fa-search me-2"></i>Caută
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="objectSearchModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-folder-open me-2"></i>Căutare după Obiect
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="objectSearchForm">
                    <div class="mb-3">
                        <label for="obiectDosar" class="form-label">Obiect Dosar</label>
                        <input type="text" class="form-control" id="obiectDosar" name="obiectDosar" 
                               placeholder="Ex: divorț, executare silită" required>
                    </div>
                    <div class="mb-3">
                        <label for="institutieObiect" class="form-label">Instituție (opțional)</label>
                        <select class="form-select" id="institutieObiect" name="institutie">
                            <option value="">Toate instituțiile</option>
                            {% for cod, nume in instante %}
                                <option value="{{ cod }}">{{ nume }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anulează</button>
                <button type="button" class="btn btn-warning" onclick="searchByObject()">
                    <i class="fas fa-search me-2"></i>Caută
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Quick search functions
function showNumberSearch() {
    new bootstrap.Modal(document.getElementById('numberSearchModal')).show();
}

function showPartySearch() {
    new bootstrap.Modal(document.getElementById('partySearchModal')).show();
}

function showObjectSearch() {
    new bootstrap.Modal(document.getElementById('objectSearchModal')).show();
}

function searchByNumber() {
    const numberForm = document.getElementById('numberSearchForm');
    const numberInput = document.getElementById('numarDosar');
    const institutieSelect = document.getElementById('institutieNumar');
    const mainForm = document.getElementById('searchForm');
    const bulkSearchTerms = document.getElementById('bulkSearchTerms');
    const mainInstitutieSelect = document.getElementById('institutie');
    
    if (numberInput.value.trim() === '') {
        showNotification('Vă rugăm introduceți numărul dosarului.', 'error');
        return;
    }

    // Populează formularul principal cu termenul de căutare și instituția
    bulkSearchTerms.value = numberInput.value.trim();
    mainInstitutieSelect.value = institutieSelect.value;

    // Închide modala
    const modal = bootstrap.Modal.getInstance(document.getElementById('numberSearchModal'));
    modal.hide();

    // Trimite formularul principal
    mainForm.submit();
}

function searchByParty() {
    const partyForm = document.getElementById('partySearchForm');
    const partyInput = document.getElementById('numeParte');
    const institutieSelect = document.getElementById('institutieParte');
    const mainForm = document.getElementById('searchForm');
    const bulkSearchTerms = document.getElementById('bulkSearchTerms');
    const mainInstitutieSelect = document.getElementById('institutie');

    if (partyInput.value.trim() === '') {
        showNotification('Vă rugăm introduceți numele părții.', 'error');
        return;
    }

    // Populează formularul principal cu termenul de căutare și instituția
    bulkSearchTerms.value = partyInput.value.trim();
    mainInstitutieSelect.value = institutieSelect.value;

    // Închide modala
    const modal = bootstrap.Modal.getInstance(document.getElementById('partySearchModal'));
    modal.hide();

    // Trimite formularul principal
    mainForm.submit();
}

function searchByObject() {
    const objectForm = document.getElementById('objectSearchForm');
    const objectInput = document.getElementById('obiectDosar');
    const institutieSelect = document.getElementById('institutieObiect');
    const mainForm = document.getElementById('searchForm');
    const bulkSearchTerms = document.getElementById('bulkSearchTerms');
    const mainInstitutieSelect = document.getElementById('institutie');

     if (objectInput.value.trim() === '') {
        showNotification('Vă rugăm introduceți obiectul dosarului.', 'error');
        return;
    }

    // Populează formularul principal cu termenul de căutare și instituția
    bulkSearchTerms.value = objectInput.value.trim();
    mainInstitutieSelect.value = institutieSelect.value;

    // Închide modala
    const modal = bootstrap.Modal.getInstance(document.getElementById('objectSearchModal'));
    modal.hide();

    // Trimite formularul principal
    mainForm.submit();
}

function showNotification(message, type) {
    const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
    const icon = type === 'error' ? 'exclamation-triangle' : 'check-circle';
    
    const alert = document.createElement('div');
    alert.className = `alert ${alertClass} alert-dismissible fade show`;
    alert.innerHTML = `
        <i class="fas fa-${icon} me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('main').insertBefore(alert, document.querySelector('main').firstChild);
    
    setTimeout(() => {
        alert.remove();
    }, 5000);
}


</script>
{% endblock %}

