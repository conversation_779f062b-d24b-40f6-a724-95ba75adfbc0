<?php

namespace App;

/**
 * Router simplu pentru aplicație
 */
class Router
{
    private array $routes = [];
    private string $notFoundHandler = '';

    /**
     * <PERSON>ugă o rută GET
     */
    public function get(string $path, string $handler): void
    {
        $this->routes['GET'][$path] = $handler;
    }

    /**
     * Adaugă o rută POST
     */
    public function post(string $path, string $handler): void
    {
        $this->routes['POST'][$path] = $handler;
    }

    /**
     * Setează handler-ul pentru rutele negăsite
     */
    public function setNotFoundHandler(string $handler): void
    {
        $this->notFoundHandler = $handler;
    }

    /**
     * Procesează cererea curentă
     */
    public function dispatch(): void
    {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        
        // Eliminăm directorul din path dacă există
        $basePath = dirname($_SERVER['SCRIPT_NAME']);
        if ($basePath !== '/') {
            $path = str_replace($basePath, '', $path);
        }
        
        // Eliminăm slash-ul final
        $path = rtrim($path, '/');
        if (empty($path)) {
            $path = '/';
        }

        // Căutăm ruta
        if (isset($this->routes[$method][$path])) {
            $handler = $this->routes[$method][$path];
            $this->executeHandler($handler);
            return;
        }

        // Încercăm să găsim o rută cu parametri
        foreach ($this->routes[$method] ?? [] as $route => $handler) {
            $pattern = $this->convertRouteToRegex($route);
            if (preg_match($pattern, $path, $matches)) {
                array_shift($matches); // Eliminăm match-ul complet
                $this->executeHandler($handler, $matches);
                return;
            }
        }

        // Ruta nu a fost găsită
        if ($this->notFoundHandler) {
            $this->executeHandler($this->notFoundHandler);
        } else {
            http_response_code(404);
            echo '404 - Pagina nu a fost găsită';
        }
    }

    /**
     * Convertește o rută cu parametri în regex
     */
    private function convertRouteToRegex(string $route): string
    {
        return '#^' . preg_replace('#\{([a-zA-Z]+)\}#', '([^/]+)', $route) . '$#';
    }

    /**
     * Execută handler-ul
     */
    private function executeHandler(string $handler, array $params = []): void
    {
        if (strpos($handler, '@') !== false) {
            [$controller, $method] = explode('@', $handler);
            $controllerClass = "App\\Controllers\\{$controller}";
            
            if (class_exists($controllerClass)) {
                $controllerInstance = new $controllerClass();
                if (method_exists($controllerInstance, $method)) {
                    call_user_func_array([$controllerInstance, $method], $params);
                    return;
                }
            }
        }

        // Handler simplu - funcție sau fișier
        if (function_exists($handler)) {
            call_user_func_array($handler, $params);
        } elseif (file_exists($handler)) {
            require $handler;
        } else {
            throw new \Exception("Handler invalid: {$handler}");
        }
    }
} 