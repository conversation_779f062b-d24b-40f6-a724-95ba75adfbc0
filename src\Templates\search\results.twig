{% extends "layouts/main.twig" %}

{% block title %}Rezultate Căutare - Portal Dosare Judecătorești{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Header Section -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 text-primary">
                    <i class="fas fa-search me-2"></i>Rezultate Căutare
                </h1>
                <p class="text-muted mb-0">
                    {% if totalResults > 0 %}
                        Găsite {{ totalResults }} rezultate
                    {% else %}
                        Nu au fost găsite rezultate
                    {% endif %}
                </p>
            </div>
            <div>
                <a href="/" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Înapoi la Căutare
                </a>
            </div>
        </div>

        <!-- Search Summary -->
        {% if searchTerms or advancedFilters %}
        <div class="card mb-4 border-0 bg-light">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>Criterii de Căutare
                </h6>
                <div class="row">
                    {% if searchTerms %}
                    <div class="col-md-6">
                        <strong>Termeni de căutare:</strong>
                        <div class="text-muted">{{ searchTerms|nl2br }}</div>
                    </div>
                    {% endif %}
                    
                    {% if advancedFilters %}
                    <div class="col-md-6">
                        <strong>Filtre avansate:</strong>
                        <ul class="list-unstyled text-muted">
                            {% if advancedFilters.institutie %}
                                <li><i class="fas fa-building me-1"></i>Instituție: {{ advancedFilters.institutie }}</li>
                            {% endif %}
                            {% if advancedFilters.categorieInstanta %}
                                <li><i class="fas fa-gavel me-1"></i>Categorie instanță: {{ advancedFilters.categorieInstanta }}</li>
                            {% endif %}
                            {% if advancedFilters.categorieCaz %}
                                <li><i class="fas fa-folder me-1"></i>Categorie caz: {{ advancedFilters.categorieCaz }}</li>
                            {% endif %}
                            {% if advancedFilters.dataInceput or advancedFilters.dataSfarsit %}
                                <li><i class="fas fa-calendar me-1"></i>Perioada: {{ advancedFilters.dataInceput }} - {{ advancedFilters.dataSfarsit }}</li>
                            {% endif %}
                        </ul>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Export Buttons -->
        {% if totalResults > 0 %}
        <div class="d-flex justify-content-end mb-3">
            <div class="btn-group" role="group">
                <a href="/search/export/csv" class="btn btn-outline-success">
                    <i class="fas fa-file-csv me-2"></i>Export CSV
                </a>
                <a href="/search/export/excel" class="btn btn-outline-primary">
                    <i class="fas fa-file-excel me-2"></i>Export Excel
                </a>
            </div>
        </div>
        {% endif %}

        <!-- Results -->
        {% if results %}
            {% for result in results %}
            <div class="card mb-4 border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            Căutare: "{{ result.term }}"
                            <span class="badge bg-light text-primary ms-2">{{ result.type }}</span>
                        </h6>
                        <span class="badge bg-light text-primary">
                            {{ result.count }} rezultate
                        </span>
                    </div>
                </div>
                
                {% if result.error %}
                <div class="card-body">
                    <div class="alert alert-warning mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {{ result.error }}
                    </div>
                </div>
                {% else %}
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Număr Dosar</th>
                                    <th>Instituție</th>
                                    <th>Data</th>
                                    <th>Obiect</th>
                                    <th>Stadiu</th>
                                    <th>Acțiuni</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dosar in result.results %}
                                <tr>
                                    <td>
                                        <strong>{{ dosar.numar }}</strong>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ dosar.institutie }}</small>
                                    </td>
                                    <td>
                                        <small>{{ dosar.data }}</small>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;" title="{{ dosar.obiect }}">
                                            {{ dosar.obiect }}
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ dosar.stadiuProcesual }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="/case/{{ dosar.numar }}/{{ dosar.institutie }}" 
                                               class="btn btn-outline-primary" 
                                               title="Vezi detalii">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="/case/{{ dosar.numar }}/{{ dosar.institutie }}/pdf" 
                                               class="btn btn-outline-danger" 
                                               title="Descarcă PDF">
                                                <i class="fas fa-file-pdf"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        {% else %}
            <!-- No Results -->
            <div class="text-center py-5">
                <i class="fas fa-search fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">Nu au fost găsite rezultate</h4>
                <p class="text-muted">
                    Încercați să modificați criteriile de căutare sau să folosiți termeni mai generali.
                </p>
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i>Înapoi la Căutare
                </a>
            </div>
        {% endif %}

        <!-- Pagination -->
        {% if pagination is defined and pagination.totalPages > 1 %}
        <nav aria-label="Paginare rezultate">
            <ul class="pagination justify-content-center">
                {% if pagination.currentPage > 1 %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ pagination.currentPage - 1 }}">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                {% endif %}

                {% for page in pagination.pages %}
                    {% if page == pagination.currentPage %}
                    <li class="page-item active">
                        <span class="page-link">{{ page }}</span>
                    </li>
                    {% else %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page }}">{{ page }}</a>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if pagination.currentPage < pagination.totalPages %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ pagination.currentPage + 1 }}">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-hide alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
});

// Export functionality
function exportResults(format) {
    const url = format === 'csv' ? '/search/export/csv' : '/search/export/excel';
    window.location.href = url;
}

// Copy case number to clipboard
function copyCaseNumber(numar) {
    navigator.clipboard.writeText(numar).then(() => {
        showNotification('Numărul dosarului a fost copiat în clipboard', 'success');
    });
}

function showNotification(message, type) {
    const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
    const icon = type === 'error' ? 'exclamation-triangle' : 'check-circle';
    
    const alert = document.createElement('div');
    alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="fas fa-${icon} me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alert);
    
    setTimeout(() => {
        alert.remove();
    }, 3000);
}
</script>
{% endblock %} 