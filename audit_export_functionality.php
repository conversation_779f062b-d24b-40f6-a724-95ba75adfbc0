<?php
/**
 * Script de audit pentru funcționalitatea de export
 * Testează exporturile PDF, Excel, CSV și Email
 */

// Configurare pentru capturarea erorilor
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Încărcăm configurația
require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "AUDIT FUNCȚIONALITATE EXPORT\n";
echo "=============================\n";
echo "Data: " . date('Y-m-d H:i:s') . "\n\n";

// Verificare dependințe pentru export
echo "=== VERIFICARE DEPENDINȚE EXPORT ===\n";

// Verificare TCPDF
if (class_exists('TCPDF')) {
    echo "✅ TCPDF este disponibil\n";
    $tcpdfVersion = TCPDF_STATIC::getTCPDFVersion();
    echo "   Versiune: $tcpdfVersion\n";
} else {
    echo "❌ TCPDF nu este disponibil\n";
}

// Verificare PhpSpreadsheet
if (class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
    echo "✅ PhpSpreadsheet este disponibil\n";
} else {
    echo "❌ PhpSpreadsheet nu este disponibil\n";
}

// Verificare PHPMailer
if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
    echo "✅ PHPMailer este disponibil\n";
} else {
    echo "❌ PHPMailer nu este disponibil\n";
}

// Verificare directoare pentru export
echo "\n=== VERIFICARE DIRECTOARE EXPORT ===\n";
$exportDirs = [
    'temp' => './temp',
    'cache' => './cache',
    'logs' => './logs'
];

foreach ($exportDirs as $name => $path) {
    if (is_dir($path)) {
        $writable = is_writable($path);
        echo ($writable ? "✅" : "❌") . " Directorul $name ($path): " . 
             ($writable ? "accesibil și scriptibil" : "nu este scriptibil") . "\n";
    } else {
        echo "❌ Directorul $name ($path) nu există\n";
    }
}

// Test servicii de export
echo "\n=== TEST SERVICII EXPORT ===\n";

// Verificare PdfService
if (class_exists('App\Services\PdfService')) {
    echo "✅ PdfService este disponibil\n";
    try {
        $pdfService = new \App\Services\PdfService();
        echo "   PdfService inițializat cu succes\n";
    } catch (Exception $e) {
        echo "❌ Eroare la inițializarea PdfService: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ PdfService nu este disponibil\n";
}

// Test date de exemplu pentru export
echo "\n=== PREGĂTIRE DATE TEST ===\n";
$testData = [
    [
        'numar' => '1234/2023',
        'institutie' => 'Tribunalul București',
        'data' => '2023-01-15',
        'obiect' => 'Acțiune în divorț',
        'parti' => 'Popescu Ion vs Popescu Maria',
        'stadiu' => 'În curs de judecată'
    ],
    [
        'numar' => '5678/2023',
        'institutie' => 'Curtea de Apel București',
        'data' => '2023-02-20',
        'obiect' => 'Apel civil',
        'parti' => 'SC Test SRL vs Ionescu Gheorghe',
        'stadiu' => 'Finalizat'
    ]
];

echo "Date test pregătite: " . count($testData) . " înregistrări\n";

// Test export CSV
echo "\n=== TEST EXPORT CSV ===\n";
try {
    $csvFile = './temp/test_export_' . date('Y-m-d_H-i-s') . '.csv';
    $fp = fopen($csvFile, 'w');
    
    if ($fp) {
        // Adăugăm BOM pentru UTF-8
        fwrite($fp, "\xEF\xBB\xBF");
        
        // Header CSV
        $headers = ['Număr Dosar', 'Instituție', 'Data', 'Obiect', 'Părți', 'Stadiu'];
        fputcsv($fp, $headers, ';', '"');
        
        // Date
        foreach ($testData as $row) {
            $csvRow = [
                $row['numar'],
                $row['institutie'],
                $row['data'],
                $row['obiect'],
                $row['parti'],
                $row['stadiu']
            ];
            fputcsv($fp, $csvRow, ';', '"');
        }
        
        fclose($fp);
        
        if (file_exists($csvFile)) {
            $fileSize = filesize($csvFile);
            echo "✅ Export CSV reușit\n";
            echo "   Fișier: $csvFile\n";
            echo "   Dimensiune: $fileSize bytes\n";
            
            // Verificare conținut
            $content = file_get_contents($csvFile);
            $hasUtf8Bom = substr($content, 0, 3) === "\xEF\xBB\xBF";
            echo "   UTF-8 BOM: " . ($hasUtf8Bom ? "✅" : "❌") . "\n";
            
            // Curățare fișier test
            unlink($csvFile);
        } else {
            echo "❌ Fișierul CSV nu a fost creat\n";
        }
    } else {
        echo "❌ Nu s-a putut deschide fișierul CSV pentru scriere\n";
    }
} catch (Exception $e) {
    echo "❌ Eroare la export CSV: " . $e->getMessage() . "\n";
}

// Test export Excel
echo "\n=== TEST EXPORT EXCEL ===\n";
try {
    if (class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // Setare header
        $headers = ['Număr Dosar', 'Instituție', 'Data', 'Obiect', 'Părți', 'Stadiu'];
        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($col . '1', $header);
            $col++;
        }
        
        // Setare date
        $row = 2;
        foreach ($testData as $data) {
            $sheet->setCellValue('A' . $row, $data['numar']);
            $sheet->setCellValue('B' . $row, $data['institutie']);
            $sheet->setCellValue('C' . $row, $data['data']);
            $sheet->setCellValue('D' . $row, $data['obiect']);
            $sheet->setCellValue('E' . $row, $data['parti']);
            $sheet->setCellValue('F' . $row, $data['stadiu']);
            $row++;
        }
        
        // Salvare fișier
        $excelFile = './temp/test_export_' . date('Y-m-d_H-i-s') . '.xlsx';
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($excelFile);
        
        if (file_exists($excelFile)) {
            $fileSize = filesize($excelFile);
            echo "✅ Export Excel reușit\n";
            echo "   Fișier: $excelFile\n";
            echo "   Dimensiune: $fileSize bytes\n";
            
            // Curățare fișier test
            unlink($excelFile);
        } else {
            echo "❌ Fișierul Excel nu a fost creat\n";
        }
    } else {
        echo "❌ PhpSpreadsheet nu este disponibil pentru testare\n";
    }
} catch (Exception $e) {
    echo "❌ Eroare la export Excel: " . $e->getMessage() . "\n";
}

// Test export PDF
echo "\n=== TEST EXPORT PDF ===\n";
try {
    if (class_exists('TCPDF')) {
        // Configurare TCPDF
        $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
        
        // Setare informații document
        $pdf->SetCreator('Portal Judiciar');
        $pdf->SetAuthor('Portal Judiciar');
        $pdf->SetTitle('Test Export PDF');
        
        // Setare font
        $pdf->SetFont('helvetica', '', 10);
        
        // Adăugare pagină
        $pdf->AddPage();
        
        // Conținut HTML
        $html = '<h1>Test Export PDF</h1>';
        $html .= '<table border="1" cellpadding="4">';
        $html .= '<tr><th>Număr Dosar</th><th>Instituție</th><th>Data</th><th>Obiect</th></tr>';
        
        foreach ($testData as $row) {
            $html .= '<tr>';
            $html .= '<td>' . htmlspecialchars($row['numar']) . '</td>';
            $html .= '<td>' . htmlspecialchars($row['institutie']) . '</td>';
            $html .= '<td>' . htmlspecialchars($row['data']) . '</td>';
            $html .= '<td>' . htmlspecialchars($row['obiect']) . '</td>';
            $html .= '</tr>';
        }
        $html .= '</table>';
        
        $pdf->writeHTML($html, true, false, true, false, '');
        
        // Salvare fișier
        $pdfFile = './temp/test_export_' . date('Y-m-d_H-i-s') . '.pdf';
        $pdf->Output($pdfFile, 'F');
        
        if (file_exists($pdfFile)) {
            $fileSize = filesize($pdfFile);
            echo "✅ Export PDF reușit\n";
            echo "   Fișier: $pdfFile\n";
            echo "   Dimensiune: $fileSize bytes\n";
            
            // Curățare fișier test
            unlink($pdfFile);
        } else {
            echo "❌ Fișierul PDF nu a fost creat\n";
        }
    } else {
        echo "❌ TCPDF nu este disponibil pentru testare\n";
    }
} catch (Exception $e) {
    echo "❌ Eroare la export PDF: " . $e->getMessage() . "\n";
}

// Test configurare email
echo "\n=== TEST CONFIGURARE EMAIL ===\n";
try {
    if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
        $mail = new \PHPMailer\PHPMailer\PHPMailer(true);
        
        // Configurare SMTP (test fără trimitere)
        $mail->isSMTP();
        $mail->Host = 'localhost';
        $mail->SMTPAuth = false;
        $mail->Port = 25;
        
        echo "✅ PHPMailer configurat cu succes\n";
        echo "   Host SMTP: localhost\n";
        echo "   Port: 25\n";
        echo "   Autentificare: Nu\n";
    } else {
        echo "❌ PHPMailer nu este disponibil\n";
    }
} catch (Exception $e) {
    echo "❌ Eroare la configurarea PHPMailer: " . $e->getMessage() . "\n";
}

// Test rate limiting pentru email
echo "\n=== TEST RATE LIMITING EMAIL ===\n";
$rateLimitFile = './temp/rate_limit_test.txt';
$currentTime = time();
$maxEmails = 5;
$timeWindow = 3600; // 1 oră

// Simulăm verificarea rate limiting
$emailsSent = 0;
if (file_exists($rateLimitFile)) {
    $lastSentTime = (int)file_get_contents($rateLimitFile);
    if ($currentTime - $lastSentTime < $timeWindow) {
        $emailsSent = 3; // Simulăm 3 email-uri trimise
    }
}

$canSendEmail = $emailsSent < $maxEmails;
echo ($canSendEmail ? "✅" : "❌") . " Rate limiting email: ";
echo "$emailsSent/$maxEmails email-uri trimise în ultima oră\n";

if (file_exists($rateLimitFile)) {
    unlink($rateLimitFile);
}

echo "\n=== SUMAR AUDIT EXPORT ===\n";
echo "Testarea funcționalității de export completă.\n";
echo "Verificați rezultatele de mai sus pentru identificarea problemelor.\n";
?>
