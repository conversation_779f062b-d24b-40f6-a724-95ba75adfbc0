<?php
// Test simplu pentru autoloader
require_once __DIR__ . '/vendor/autoload.php';

try {
    $config = \App\Config\AppConfig::getInstance();
    echo "Configurația funcționează!\n";
    echo "App Name: " . $config->get('app.name') . "\n";
} catch (Exception $e) {
    echo "Eroare: " . $e->getMessage() . "\n";
    echo "Fișier: " . $e->getFile() . "\n";
    echo "Linia: " . $e->getLine() . "\n";
} 