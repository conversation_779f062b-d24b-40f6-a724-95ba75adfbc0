# Portal Judiciar

Aplicație web modernă pentru căutarea și vizualizarea dosarelor judecătorești din România.

## 🚀 Caracteristici

- **Arhitectură modernă** cu pattern MVC și routing
- **Căutare avansată** cu multiple filtre și criterii
- **Export în PDF și Excel** pentru rezultate
- **API REST** pentru integrare cu alte aplicații
- **Securitate îmbunătățită** cu validare și sanitizare
- **Logging structurat** pentru debugging și monitorizare
- **Configurație flexibilă** bazată pe mediu

## 📋 Cerințe

- PHP 7.4 sau mai nou
- Extensia SOAP pentru PHP
- Extensia mbstring pentru PHP
- Extensia intl pentru PHP (opțional, pentru gestionarea avansată a diacriticelor)
- Composer pentru gestionarea dependințelor
- Server web (Apache/Nginx)

## 🛠️ Instalare

1. **Clonează repository-ul:**
   ```bash
   git clone https://github.com/your-username/portal-judiciar.git
   cd portal-judiciar
   ```

2. **Instalează dependințele:**
   ```bash
   composer install
   ```

3. **Configurează variabilele de mediu:**
   ```bash
   cp env.example .env
   # Editează .env cu configurațiile tale
   ```

4. **Configurează serverul web:**
   - Point document root la directorul `public/`
   - Asigură-te că mod_rewrite este activat (pentru Apache)

5. **Setări permisiuni:**
   ```bash
   chmod -R 755 logs/ cache/ temp/
   ```

## 🏗️ Structura Proiectului

```
portal-judiciar/
├── public/                 # Directorul public accesibil prin web
│   ├── index.php           # Punctul de intrare în aplicație
│   ├── assets/             # Resurse statice (CSS, JS, imagini)
│   └── .htaccess           # Configurare Apache
├── src/                    # Codul sursă al aplicației
│   ├── Config/             # Configurații
│   │   └── AppConfig.php   # Configurație unificată
│   ├── Controllers/        # Controllere
│   │   ├── BaseController.php
│   │   ├── SearchController.php
│   │   └── CaseController.php
│   ├── Services/           # Servicii pentru logica de business
│   │   ├── DosarService.php
│   │   └── PdfService.php
│   ├── Models/             # Modele de date
│   ├── Helpers/            # Funcții helper
│   └── Templates/          # Șabloane Twig
├── routes.php              # Configurarea rutelor
├── bootstrap.php           # Inițializarea aplicației
├── composer.json           # Dependințe Composer
├── env.example             # Exemplu configurare mediu
├── logs/                   # Loguri
├── cache/                  # Fișiere cache
└── temp/                   # Fișiere temporare
```

## 🔧 Configurare

### Variabile de Mediu

Creează un fișier `.env` bazat pe `env.example`:

```env
# Configurații pentru aplicație
APP_ENV=development
APP_DEBUG=true
APP_TIMEZONE=Europe/Bucharest

# Configurații pentru email
MAIL_HOST=localhost
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-password
```

### Configurare Apache

Asigură-te că mod_rewrite este activat și că document root-ul pointează la directorul `public/`.

### Configurare Nginx

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/portal-judiciar/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

## 🚀 Utilizare

### Rute Disponibile

- `GET /` - Pagina principală de căutare
- `POST /search/bulk` - Căutare în masă
- `GET /search/export/csv` - Export CSV
- `GET /search/export/excel` - Export Excel
- `GET /case/{numar}/{institutie}` - Detalii dosar
- `GET /case/{numar}/{institutie}/pdf` - PDF dosar
- `POST /case/send-email` - Trimite dosar prin email

### API Endpoints

- `GET /api/institutions` - Lista instituțiilor
- `GET /api/search` - Căutare API

## 🧪 Testare

```bash
# Rularea testelor
composer test

# Verificarea stilului de codare
composer check-style

# Corectarea automată a stilului
composer fix-style
```

## 📝 Logging

Logurile sunt stocate în directorul `logs/`:

- `php_errors.log` - Erori PHP
- `soap_calls.log` - Apeluri SOAP
- `application.log` - Loguri aplicație

## 🔒 Securitate

- Validarea și sanitizarea tuturor input-urilor
- Protecție CSRF pentru formulare
- Headere de securitate HTTP
- Configurare bazată pe mediu pentru afișarea erorilor

## 🤝 Contribuții

1. Fork repository-ul
2. Creează un branch pentru funcționalitatea nouă (`git checkout -b feature/amazing-feature`)
3. Fă commit la modificări (`git commit -m 'Add some amazing feature'`)
4. Push la branch (`git push origin feature/amazing-feature`)
5. Deschide un Pull Request

## 📄 Licență

Acest proiect este licențiat sub [MIT License](LICENSE).

## 🆘 Suport

Pentru suport și întrebări, deschide un issue pe GitHub sau contactează echipa de dezvoltare.

---

**Notă:** Această versiune reprezintă o refactorizare majoră a aplicației, cu îmbunătățiri semnificative ale arhitecturii, securității și menținabilității codului.
