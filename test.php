<?php
/**
 * Test pentru noua arhitectură
 */

// Încărcăm bootstrap-ul
require_once __DIR__ . '/bootstrap.php';

// Testăm configurația
$config = new \App\Config\AppConfig();
echo "Configurația funcționează!\n";
echo "App Name: " . $config->get('app.name') . "\n";
echo "SOAP WSDL: " . $config->get('soap.wsdl') . "\n";

// Testăm router-ul
$router = new \App\Router();
echo "Router-ul funcționează!\n";

// Testăm template engine-ul
$templateEngine = new \App\Helpers\TemplateEngine();
echo "Template Engine funcționează!\n";

echo "Toate componentele funcționează corect!\n"; 