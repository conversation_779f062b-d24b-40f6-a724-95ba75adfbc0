<?php

namespace App\Helpers;

use Twig\Environment;
use Twig\Loader\FilesystemLoader;
use Twig\TwigFunction;
use Twig\Error\LoaderError;
use Twig\Error\RuntimeError;
use Twig\Error\SyntaxError;

/**
 * Clasă pentru gestionarea șabloanelor Twig
 */
class TemplateEngine
{
    /**
     * Instanța Twig Environment
     * @var Environment
     */
    private $twig;

    /**
     * Constructor
     */
    public function __construct()
    {
        // Inițializăm loader-ul Twig cu directorul de șabloane
        $loader = new FilesystemLoader(dirname(__DIR__) . '/Templates');
        
        // Creăm instanța Twig Environment
        // Folosim configuratia din AppConfig
        $config = \App\Config\AppConfig::getInstance();
        $cacheEnabled = $config->get('cache.enabled', false); // Default to false if not set
        $cacheDir = $config->get('app.cache_dir');

        $this->twig = new Environment($loader, [
            'cache' => $cacheEnabled ? $cacheDir . '/twig' : false,
            'debug' => $config->isDebug(), // Use debug setting from config
            'auto_reload' => $config->isDebug() // Auto-reload in debug mode
        ]);

        // Adăugăm funcții personalizate
        $this->addCustomFunctions();
    }

    /**
     * Adaugă funcții personalizate în Twig
     */
    private function addCustomFunctions()
    {
        // Funcție pentru generarea URL-urilor
        $this->twig->addFunction(new TwigFunction('url', function ($path, $params = []) {
            $url = $path;
            if (!empty($params)) {
                $url .= '?' . http_build_query($params);
            }
            return $url;
        }));
        
        // Funcție pentru verificarea dacă un string conține un substring
        $this->twig->addFunction(new TwigFunction('str_contains', function ($haystack, $needle) {
            return strpos($haystack, $needle) !== false;
        }));
        
        // Funcție pentru formatarea datelor
        $this->twig->addFunction(new TwigFunction('format_date', function ($date, $format = 'd.m.Y') {
            if (empty($date)) {
                return '';
            }
            
            try {
                $dateObj = new \DateTime($date);
                return $dateObj->format($format);
            } catch (\Exception $e) {
                return $date;
            }
        }));
        
        // Funcție pentru truncarea textului
        $this->twig->addFunction(new TwigFunction('truncate', function ($text, $length = 100, $suffix = '...') {
            if (strlen($text) <= $length) {
                return $text;
            }
            
            return substr($text, 0, $length) . $suffix;
        }));
        
        // Funcție pentru verificarea dacă o valoare este goală
        $this->twig->addFunction(new TwigFunction('is_empty', function ($value) {
            return empty($value);
        }));
        
        // Funcție pentru obținerea valorii din sesiune
        $this->twig->addFunction(new TwigFunction('session', function ($key, $default = null) {
            return $_SESSION[$key] ?? $default;
        }));
        
        // Funcție pentru obținerea valorii din GET
        $this->twig->addFunction(new TwigFunction('get', function ($key, $default = null) {
            return $_GET[$key] ?? $default;
        }));
        
        // Funcție pentru obținerea valorii din POST
        $this->twig->addFunction(new TwigFunction('post', function ($key, $default = null) {
            return $_POST[$key] ?? $default;
        }));
    }

    /**
     * Randează un șablon cu datele specificate
     *
     * @param string $template Numele șablonului
     * @param array $data Datele pentru șablon
     * @return string Șablonul randat
     * @throws LoaderError
     * @throws RuntimeError
     * @throws SyntaxError
     */
    public function render($template, $data = [])
    {
        // Adăugăm variabile globale
        $data['app'] = [
            'name' => 'Portal Judiciar',
            'version' => '1.0.0',
            'year' => date('Y')
        ];
        
        // Adăugăm mesajele flash din sesiune
        $data['flash'] = $_SESSION['flash'] ?? [];
        
        // Ștergem mesajele flash după ce le-am adăugat în șablon
        unset($_SESSION['flash']);
        
        // Randăm șablonul
        return $this->twig->render($template, $data);
    }

    /**
     * Adaugă un mesaj flash în sesiune
     *
     * @param string $type Tipul mesajului (success, error, warning, info)
     * @param string $message Mesajul
     */
    public static function addFlashMessage($type, $message)
    {
        if (!isset($_SESSION['flash'])) {
            $_SESSION['flash'] = [];
        }
        
        $_SESSION['flash'][] = [
            'type' => $type,
            'message' => $message
        ];
    }
}
