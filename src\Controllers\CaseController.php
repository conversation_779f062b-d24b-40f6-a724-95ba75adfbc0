<?php

namespace App\Controllers;

use App\Services\DosarService;
use App\Services\PdfService;

/**
 * Controller pentru funcționalitățile legate de dosare
 */
class CaseController extends BaseController
{
    private DosarService $dosarService;
    private PdfService $pdfService;

    public function __construct()
    {
        parent::__construct();
        $this->dosarService = new DosarService();
        $this->pdfService = new PdfService();
    }

    /**
     * Afișează detaliile unui dosar
     */
    public function show(string $numar, string $institutie): void
    {
        try {
            $dosar = $this->dosarService->getDetaliiDosar($numar, $institutie);
            
            if (empty($dosar)) {
                $this->redirect('/search?error=Nu s-a găsit dosarul');
                return;
            }

            $data = [
                'dosar' => $dosar,
                'pageTitle' => "Dosar {$numar} - Detalii"
            ];

            echo $this->templateEngine->render('case/details.twig', $data);

        } catch (\Exception $e) {
            error_log('Eroare la afișarea dosarului: ' . $e->getMessage());
            $this->redirect('/search?error=Eroare la încărcarea dosarului');
        }
    }

    /**
     * Generează PDF pentru un dosar
     */
    public function generatePdf(string $numar, string $institutie): void
    {
        try {
            $dosar = $this->dosarService->getDetaliiDosar($numar, $institutie);
            
            if (empty($dosar)) {
                $this->json(['error' => 'Nu s-a găsit dosarul'], 404);
                return;
            }

            $filename = $this->generatePdfFilename($numar);
            
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            
            $this->pdfService->generateCasePdf($dosar, $filename);

        } catch (\Exception $e) {
            error_log('Eroare la generarea PDF: ' . $e->getMessage());
            $this->json(['error' => 'Eroare la generarea PDF'], 500);
        }
    }

    /**
     * Afișează PDF-ul inline
     */
    public function showPdf(string $numar, string $institutie): void
    {
        try {
            $dosar = $this->dosarService->getDetaliiDosar($numar, $institutie);
            
            if (empty($dosar)) {
                $this->json(['error' => 'Nu s-a găsit dosarul'], 404);
                return;
            }

            header('Content-Type: application/pdf');
            
            $this->pdfService->generateCasePdf($dosar, null, true);

        } catch (\Exception $e) {
            error_log('Eroare la afișarea PDF: ' . $e->getMessage());
            $this->json(['error' => 'Eroare la afișarea PDF'], 500);
        }
    }

    /**
     * Trimite detaliile dosarului prin email
     */
    public function sendEmail(): void
    {
        $postData = $this->getPostData();
        
        $rules = [
            'email' => 'required|email',
            'numar' => 'required',
            'institutie' => 'required'
        ];

        $errors = $this->validate($postData, $rules);
        
        if (!empty($errors)) {
            $this->json(['errors' => $errors], 400);
            return;
        }

        try {
            $dosar = $this->dosarService->getDetaliiDosar(
                $postData['numar'],
                $postData['institutie']
            );
            
            if (empty($dosar)) {
                $this->json(['error' => 'Nu s-a găsit dosarul'], 404);
                return;
            }

            $this->pdfService->sendCaseByEmail($dosar, $postData['email']);
            
            $this->json(['success' => 'Email-ul a fost trimis cu succes']);

        } catch (\Exception $e) {
            error_log('Eroare la trimiterea email-ului: ' . $e->getMessage());
            $this->json(['error' => 'Eroare la trimiterea email-ului'], 500);
        }
    }

    /**
     * Caută dosar în toate instituțiile
     */
    public function searchInAllInstitutions(string $numar): void
    {
        try {
            $results = $this->dosarService->searchDosarInAllInstitutions($numar);
            
            $this->json([
                'results' => $results,
                'count' => count($results)
            ]);

        } catch (\Exception $e) {
            $this->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Obține informații despre ședințe pentru un dosar
     */
    public function getSessions(string $numar, string $institutie): void
    {
        try {
            $dosar = $this->dosarService->getDetaliiDosar($numar, $institutie);
            
            if (empty($dosar)) {
                $this->json(['error' => 'Nu s-a găsit dosarul'], 404);
                return;
            }

            $sessions = $dosar->sedinte ?? [];
            
            $this->json([
                'sessions' => $sessions,
                'count' => count($sessions)
            ]);

        } catch (\Exception $e) {
            $this->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Generează PDF pentru ședințe
     */
    public function generateSessionsPdf(string $numar, string $institutie): void
    {
        try {
            $dosar = $this->dosarService->getDetaliiDosar($numar, $institutie);
            
            if (empty($dosar)) {
                $this->json(['error' => 'Nu s-a găsit dosarul'], 404);
                return;
            }

            $sessions = $dosar->sedinte ?? [];
            
            if (empty($sessions)) {
                $this->json(['error' => 'Nu există ședințe pentru acest dosar'], 404);
                return;
            }

            $filename = "sedinte_dosar_{$numar}_" . date('Y-m-d_H-i-s') . '.pdf';
            
            header('Content-Type: application/pdf');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            
            $this->pdfService->generateSessionsPdf($sessions, $filename, $institutie);

        } catch (\Exception $e) {
            error_log('Eroare la generarea PDF pentru ședințe: ' . $e->getMessage());
            $this->json(['error' => 'Eroare la generarea PDF'], 500);
        }
    }

    /**
     * Obține informații despre căile de atac
     */
    public function getAppeals(string $numar, string $institutie): void
    {
        try {
            $dosar = $this->dosarService->getDetaliiDosar($numar, $institutie);
            
            if (empty($dosar)) {
                $this->json(['error' => 'Nu s-a găsit dosarul'], 404);
                return;
            }

            $appeals = $dosar->caiAtac ?? [];
            
            $this->json([
                'appeals' => $appeals,
                'count' => count($appeals)
            ]);

        } catch (\Exception $e) {
            $this->json(['error' => $e->getMessage()], 500);
        }
    }

    // Metode helper private

    private function generatePdfFilename(string $numar): string
    {
        if (empty($numar)) {
            return 'Dosar_nedisponibil.pdf';
        }
        
        $cleanNumar = preg_replace('/[\/\\:*?"<>|]/', '_', $numar);
        return "Dosar_nr_{$cleanNumar}.pdf";
    }
} 