{% extends "layouts/main.twig" %}

{% block title %}API Documentation - Portal Dosare <PERSON>{% endblock %}

{% block extra_css %}
<style>
.endpoint {
    border-left: 4px solid #007bff;
    padding-left: 1rem;
    margin-bottom: 2rem;
}

.endpoint h4 {
    color: #007bff;
    margin-bottom: 0.5rem;
}

.method {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: bold;
    margin-right: 0.5rem;
}

.method.get { background-color: #28a745; color: white; }
.method.post { background-color: #007bff; color: white; }
.method.put { background-color: #ffc107; color: black; }
.method.delete { background-color: #dc3545; color: white; }

.code-block {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 1rem;
    margin: 1rem 0;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

.response-example {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 1rem;
    margin: 1rem 0;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    max-height: 400px;
    overflow-y: auto;
}

.parameter-table {
    margin: 1rem 0;
}

.parameter-table th {
    background-color: #f8f9fa;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <!-- Header -->
        <div class="text-center mb-5">
            <h1 class="display-4 text-primary">
                <i class="fas fa-code me-3"></i>API Documentation
            </h1>
            <p class="lead text-muted">
                Documentația completă pentru API-ul REST al Portalului Dosare Judecătorești
            </p>
            <div class="badge bg-success fs-6">Versiunea 1.0.0</div>
        </div>

        <!-- API Overview -->
        <div class="card mb-4 border-0 shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Informații Generale
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Base URL</h6>
                        <div class="code-block">{{ baseUrl }}/api</div>
                    </div>
                    <div class="col-md-6">
                        <h6>Format Răspuns</h6>
                        <div class="code-block">application/json</div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>Autentificare</h6>
                        <p class="text-muted">Momentan nu este necesară autentificarea pentru API.</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Rate Limiting</h6>
                        <p class="text-muted">100 de cereri per minut per IP.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Endpoints -->
        <h2 class="mb-4">
            <i class="fas fa-list me-2"></i>Endpoints
        </h2>

        <!-- Statistics -->
        <div class="endpoint">
            <h4>
                <span class="method get">GET</span>
                /api/stats
            </h4>
            <p>Obține statistici despre API și lista endpoint-urilor disponibile.</p>
            
            <h6>Parametri</h6>
            <p class="text-muted">Nu sunt necesari parametri.</p>
            
            <h6>Exemplu de cerere</h6>
            <div class="code-block">GET {{ baseUrl }}/api/stats</div>
            
            <h6>Exemplu de răspuns</h6>
            <div class="response-example">
{
  "success": true,
  "data": {
    "total_institutions": 9,
    "api_version": "1.0.0",
    "last_updated": "2024-01-15 10:30:00",
    "endpoints": {
      "GET /api/institutions": "Lista instituțiilor",
      "GET /api/search": "Căutare dosare",
      "GET /api/case/{numar}/{institutie}": "Detalii dosar"
    }
  },
  "timestamp": "2024-01-15 10:30:00"
}
            </div>
        </div>

        <!-- Institutions -->
        <div class="endpoint">
            <h4>
                <span class="method get">GET</span>
                /api/institutions
            </h4>
            <p>Obține lista tuturor instituțiilor judecătorești disponibile.</p>
            
            <h6>Parametri</h6>
            <p class="text-muted">Nu sunt necesari parametri.</p>
            
            <h6>Exemplu de cerere</h6>
            <div class="code-block">GET {{ baseUrl }}/api/institutions</div>
            
            <h6>Exemplu de răspuns</h6>
            <div class="response-example">
{
  "success": true,
  "data": {
    "JudecatoriaSECTORUL1BUCURESTI": "Judecătoria Sectorului 1 București",
    "TribunalulBUCURESTI": "Tribunalul București",
    "CurteadeApelBUCURESTI": "Curtea de Apel București"
  },
  "count": 9,
  "timestamp": "2024-01-15 10:30:00"
}
            </div>
        </div>

        <!-- Search -->
        <div class="endpoint">
            <h4>
                <span class="method get">GET</span>
                /api/search
            </h4>
            <p>Căutare dosare judecătorești cu multiple criterii.</p>
            
            <h6>Parametri</h6>
            <table class="table table-sm parameter-table">
                <thead>
                    <tr>
                        <th>Parametru</th>
                        <th>Tip</th>
                        <th>Obligatoriu</th>
                        <th>Descriere</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>q</code></td>
                        <td>string</td>
                        <td><span class="badge bg-danger">Da</span></td>
                        <td>Termenul de căutare</td>
                    </tr>
                    <tr>
                        <td><code>type</code></td>
                        <td>string</td>
                        <td><span class="badge bg-secondary">Nu</span></td>
                        <td>Tipul de căutare: number, party, object</td>
                    </tr>
                    <tr>
                        <td><code>institution</code></td>
                        <td>string</td>
                        <td><span class="badge bg-secondary">Nu</span></td>
                        <td>Codul instituției</td>
                    </tr>
                    <tr>
                        <td><code>limit</code></td>
                        <td>integer</td>
                        <td><span class="badge bg-secondary">Nu</span></td>
                        <td>Numărul de rezultate (max 100)</td>
                    </tr>
                    <tr>
                        <td><code>page</code></td>
                        <td>integer</td>
                        <td><span class="badge bg-secondary">Nu</span></td>
                        <td>Numărul paginii</td>
                    </tr>
                </tbody>
            </table>
            
            <h6>Exemplu de cerere</h6>
            <div class="code-block">GET {{ baseUrl }}/api/search?q=1234/2023&type=number&institution=JudecatoriaSECTORUL1BUCURESTI</div>
            
            <h6>Exemplu de răspuns</h6>
            <div class="response-example">
{
  "success": true,
  "data": [
    {
      "number": "1234/2023",
      "institution": "Judecătoria Sectorului 1 București",
      "date": "2023-01-15",
      "object": "Divorț",
      "status": "În curs",
      "category": "Familie",
      "last_modified": "2024-01-15 10:30:00",
      "parties_count": 2,
      "sessions_count": 3,
      "appeals_count": 0
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 1,
    "pages": 1
  },
  "query": {
    "type": "number",
    "q": "1234/2023",
    "institution": "JudecatoriaSECTORUL1BUCURESTI"
  },
  "timestamp": "2024-01-15 10:30:00"
}
            </div>
        </div>

        <!-- Case Details -->
        <div class="endpoint">
            <h4>
                <span class="method get">GET</span>
                /api/case/{numar}/{institutie}
            </h4>
            <p>Obține detaliile complete ale unui dosar.</p>
            
            <h6>Parametri în URL</h6>
            <table class="table table-sm parameter-table">
                <thead>
                    <tr>
                        <th>Parametru</th>
                        <th>Tip</th>
                        <th>Descriere</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>numar</code></td>
                        <td>string</td>
                        <td>Numărul dosarului</td>
                    </tr>
                    <tr>
                        <td><code>institutie</code></td>
                        <td>string</td>
                        <td>Codul instituției</td>
                    </tr>
                </tbody>
            </table>
            
            <h6>Exemplu de cerere</h6>
            <div class="code-block">GET {{ baseUrl }}/api/case/1234/2023/JudecatoriaSECTORUL1BUCURESTI</div>
            
            <h6>Exemplu de răspuns</h6>
            <div class="response-example">
{
  "success": true,
  "data": {
    "number": "1234/2023",
    "institution": "Judecătoria Sectorului 1 București",
    "date": "2023-01-15",
    "object": "Divorț",
    "status": "În curs",
    "category": "Familie",
    "last_modified": "2024-01-15 10:30:00",
    "parties": [
      {
        "name": "Ion Popescu",
        "role": "Reclamant"
      },
      {
        "name": "Maria Popescu",
        "role": "Reclamat"
      }
    ],
    "sessions": [
      {
        "date": "2023-02-15",
        "time": "10:00",
        "panel": "Completul I",
        "decision": "Amânare"
      }
    ],
    "appeals": []
  },
  "timestamp": "2024-01-15 10:30:00"
}
            </div>
        </div>

        <!-- Search All Institutions -->
        <div class="endpoint">
            <h4>
                <span class="method get">GET</span>
                /api/case/{numar}/search-all
            </h4>
            <p>Caută un dosar în toate instituțiile disponibile.</p>
            
            <h6>Parametri în URL</h6>
            <table class="table table-sm parameter-table">
                <thead>
                    <tr>
                        <th>Parametru</th>
                        <th>Tip</th>
                        <th>Descriere</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>numar</code></td>
                        <td>string</td>
                        <td>Numărul dosarului</td>
                    </tr>
                </tbody>
            </table>
            
            <h6>Exemplu de cerere</h6>
            <div class="code-block">GET {{ baseUrl }}/api/case/1234/2023/search-all</div>
        </div>

        <!-- Sessions -->
        <div class="endpoint">
            <h4>
                <span class="method get">GET</span>
                /api/case/{numar}/{institutie}/sessions
            </h4>
            <p>Obține ședințele pentru un dosar specific.</p>
            
            <h6>Exemplu de cerere</h6>
            <div class="code-block">GET {{ baseUrl }}/api/case/1234/2023/JudecatoriaSECTORUL1BUCURESTI/sessions</div>
        </div>

        <!-- Appeals -->
        <div class="endpoint">
            <h4>
                <span class="method get">GET</span>
                /api/case/{numar}/{institutie}/appeals
            </h4>
            <p>Obține căile de atac pentru un dosar specific.</p>
            
            <h6>Exemplu de cerere</h6>
            <div class="code-block">GET {{ baseUrl }}/api/case/1234/2023/JudecatoriaSECTORUL1BUCURESTI/appeals</div>
        </div>

        <!-- Error Handling -->
        <div class="card mt-5 border-0 shadow-sm">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Gestionarea Erorilor
                </h5>
            </div>
            <div class="card-body">
                <p>Toate erorile sunt returnate cu un format consistent:</p>
                <div class="response-example">
{
  "success": false,
  "error": "Mesajul de eroare",
  "timestamp": "2024-01-15 10:30:00"
}
                </div>
                
                <h6 class="mt-3">Coduri de stare HTTP</h6>
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Cod</th>
                            <th>Descriere</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><span class="badge bg-success">200</span></td>
                            <td>Succes</td>
                        </tr>
                        <tr>
                            <td><span class="badge bg-warning">400</span></td>
                            <td>Cerere incorectă</td>
                        </tr>
                        <tr>
                            <td><span class="badge bg-danger">404</span></td>
                            <td>Nu a fost găsit</td>
                        </tr>
                        <tr>
                            <td><span class="badge bg-danger">500</span></td>
                            <td>Eroare internă server</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Code Examples -->
        <div class="card mt-4 border-0 shadow-sm">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-code me-2"></i>Exemple de Cod
                </h5>
            </div>
            <div class="card-body">
                <h6>JavaScript (Fetch API)</h6>
                <div class="code-block">
// Căutare dosare
fetch('/api/search?q=1234/2023&type=number')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log('Rezultate:', data.data);
    } else {
      console.error('Eroare:', data.error);
    }
  });

// Obține detaliile unui dosar
fetch('/api/case/1234/2023/JudecatoriaSECTORUL1BUCURESTI')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log('Detalii dosar:', data.data);
    }
  });
                </div>

                <h6 class="mt-3">PHP (cURL)</h6>
                <div class="code-block">
// Căutare dosare
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, '/api/search?q=1234/2023&type=number');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);

$data = json_decode($response, true);
if ($data['success']) {
    echo "Rezultate: " . count($data['data']);
}
                </div>

                <h6 class="mt-3">Python (requests)</h6>
                <div class="code-block">
import requests

# Căutare dosare
response = requests.get('/api/search', params={
    'q': '1234/2023',
    'type': 'number'
})
data = response.json()

if data['success']:
    print(f"Găsite {len(data['data'])} rezultate")
else:
    print(f"Eroare: {data['error']}")
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 